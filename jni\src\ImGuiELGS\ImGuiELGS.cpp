#include "ImGuiELGS.h"
#include "font_1.h"
#include "font_2.h"
#include <unordered_set>
#include <map>
#include <string>
float dx = 20.f, yuan, zuo, you;
float ycsz, ycsd, ycsp;
float xyd;
bool show556, show762, showR<PERSON><PERSON>, showSubmachine, showSniper, showMirror, showExpansion, showOtherParts, showDrug, showArmor, 空投, 骨灰盒, 地铁;
bool 透明 = false, 观透 = false, 首次 = false;
bool aigl = false, dynamic;
bool 类名 = false, 无敌炫酷;
int 血条 = 0;
int ArraysCount;
int OwnTeam = 0;
uintptr_t 基址头;
std::unordered_map<int, ImTextureID> 手持图片;
double static_ratio = 0.2;
float Fov;
int IsCamera = 0, IsFire = 0, SelfAction = 0, 自身动作 = 0, 自身武器 = 0;
long int WorldAddress, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>;
extern int output;
int SkeletonList_New[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 13}, {4, 33}, {33, 34}, {34, 35}, {0, 55}, {55, 56}, {56, 57}, {0, 59}, {59, 60}, {60, 61}}; // 68
int SkeletonList_Old[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 63}, {4, 32}, {32, 33}, {33, 62}, {0, 52}, {52, 53}, {53, 54}, {0, 56}, {56, 57}, {57, 58}}; // 其他
int SkeletonList_Yan[][2]{{5, 4}, {4, 0}, {4, 13}, {13, 14}, {14, 15}, {4, 35}, {35, 36}, {36, 37}, {0, 57}, {57, 58}, {58, 59}, {0, 61}, {61, 62}, {62, 63}}; // 72
int SkeletonList_S30[][2]{{5, 4}, {4, 0}, {5, 6}, {6, 7}, {7, 8}, {5, 34}, {34, 35}, {35, 36}, {0, 55}, {55, 56}, {56, 57}, {0, 59}, {59, 60}, {60, 61}};      // 70
std::unordered_map<int, std::unordered_map<std::string, std::string>> itemTypeMap = {
    {1, {// 投掷物类
         {"ojGrenade_BP_C", "手雷"},
         {"BP_Pistol_RevivalFlaregun", "召回枪"},
         {"BP_Ammo_RevivalFlare_Pickup_C", "召回枪子弹"},
         {"SelectBuildActor_BP_C", "防雷系统"},
         {"Pistol_Flaregun", "信号枪"},
         {"BP_Other_Mortar_Bullet_C", "注意迫击炮"},
         {"BP_AirraidActor_C", "空袭"},
         {"BP_TraceGrenade", "追踪雷"},
         {"BP_WEP_DragonBoySpear_C", "雷枪"},
         {"BuildingActor_ConcertStage_MusicGirl_BP_C", "狗窝"},
         {"ojBurn_BP_C", "烧"}}},
    {2, {// 载具类
         {"_Mountainbike_Training_C", "自行车"},
         {"VH_HammerShark_C", "小鲨鱼"},
         {"Mirado", "双人跑车"},
         {"Scooter", "小绵羊"},
         {"VH_Horse", "马"},
         {"_BRDM_C", "装甲车"},
         {"VH_Motorcycle_C", "摩托车"},
         {"Snowmobile", "雪地摩托"},
         {"StationWagon", "旅行车"},
         {"BP_VH_Buggy", "蹦蹦车"},
         {"VH_Dacia_", "轿车"},
         {"VH_UAZ01_New_C", "吉普车"},
         {"PickUp_07_C", "皮卡车"},
         {"CoupeRB", "双人跑车"},
         {"_MiniBus_01_C", "迷你巴士"},
         {"_PG117_C", "快艇"},
         {"uaRail_1_C", "摩托艇"},
         {"_Motorglider_C", "滑翔机"},
         {"BP_VH_Bigfoot_C", "大脚车"},
         {"VH_ATV1_C", "四轮摩托"},
         {"Rony_01_C", "行李车"},
         {"VH_UTV_C", "越野车"},
         {"BP_VH_Tuk_1_C", "三轮车"},
         {"VH_Snowmobile_C", "雪橇车"},
         {"PG117", "船"},
         {"VH_4SportCar_C", "跑车"},
         {"BP_Excavator_C", "挖掘机"},
         {"VH_Kite_C", "风筝"},
         {"VH_Drift", "拉力赛车"},
         {"VH_Blanc_C", "SUV电车"},
         {"VH_Picobus_C", "大巴车"},
         {"VH_DumpTruck_C", "泥土车"},
         {"VH_Excavator_C", "挖掘机"},
         {"HugeMouthJet_RPG_C", "战斗机"},
         {"VH_LostMobile_C", "霹雳车"},
         {"VH_DesertCar_C", "沙漠越野车"}}},
    {3, {// 防具类
         {"BP_Armor_Lv3_C", "三 甲"},
         {"_MZJ_8X_Pickup_C", "8倍"},
         {"Bag_Lv3", "三 包"},
         {"_MZJ_6X_Pickup_C", "6倍"},
         {"_DJ_Large_EQ_Pickup_C", "快 扩"},
         {"DJ_Mid_EQ", "冲 快扩"},
         {"BP_Helmet_Lv3_C", "三 头"}}},
    {4, {// 道具类
         {"BP_Pickup_Finger_C", "飞索"},
         {"BP_Neon_Coin_Pickup_C", "钱"},
         {"CG030_Market_SafeBox_2_C", "保险"},
         {"BP_ShotGun_AA12_Wrapper_C", "最牛逼的喷子"},
         {"EscapeBox_SpeEffect_C", "超级物资箱"},
         {"EscapeBox_JerricanBarrel_", "油桶"},
         {"MilitarySupplyBoxBase_Baltic_Theme_C", "主题箱子"},
         {"MilitarySupplyBoxBase_Baltic_Classic_C", "主题箱子"},
         {"MilitarySupp", "主题箱子"},
         {"CG030_Market_SafeBox_C", "保险"},
         {"CG027_Lottery_C", "沙漠扭蛋机"},
         {"_revivalAED_Pickup_C", "自救器"},
         {"AirDropBox_C", "空投"},
         {"BP_Grenade_EmergencyCall_Weapon_Wrapper_C", "紧急呼救器"},
         {"BP_AirDropBox_SuperPeople_C", "空投"},
         {"AirDropListWrapperActor", "空投"},
         {"BP_AirdropChipBox_C", "金仓"},
         {"perPeopleSkill", "金插"}}},
    {5, {// 盒子
         {"_PlayerDeadListWrapper_C", "盒子"}}},
    {6, {// 药品
         {"ink_Pickup_C", "饮料"},
         {"lls_Pickup_C", "止痛"},
         {"jection_Pickup_C", "肾上腺素"},
         {"rstAidbox_Pickup_C", "医疗"}}},
    {7, {// 子弹
         {"Ammo_12Guage", "弹"}}},
    {8, {// 枪械
         {"BP_Rifle_M762_Wrapper_C", "M762"},
         {"BP_Rifle_SCAR_Wrapper_C", "SCAR"},
         {"BP_Rifle_M416_Wrapper_C", "M416"},
         {"BP_Rifle_AKM_Wrapper_C", "AKM"},
         {"BP_Rifle_ACE32_Wrapper_C", "ACE32"},
         {"BP_Rifle_HoneyBadger_Wrapper_C", "蜜獾"},
         // 冲锋枪/轻机枪 (Submachine Guns / Light Machine Guns)
         {"BP_MachineGun_UMP9_Wrapper_C", "UMP45"},
         {"BP_MachineGun_AKS74U_Wrapper_C", "AKS"},
         {"BP_MachineGun_Vector_Wrapper_C", "Vector"},
         // 其他枪械 (Other Guns)
         {"BP_Other_Mortar_Wrapper_C", "迫击炮"},
         {"BP_Other_MG36_Wrapper_C", "MG36"},
         {"BP_Other_PKM_Wrapper_C", "PKM"},
         // 霰弹枪 (Shotguns)
         {"BP_ShotGun_S12K_Wrapper_C", "S12K"},
         {"BP_ShotGun_DP12_Wrapper_C", "DBS"},
         {"BP_ShotGun_SPAS_Wrapper_C", "SPAS"},
         {"BP_ShotGun_AA12_Wrapper_C", "AA12"},
         // 狙击枪 (Sniper Rifles)
         {"BP_Sniper_SVD_Wrapper_C", "SVD"},
         {"BP_Sniper_SKS_Wrapper_C", "SKS"},
         {"BP_Rifle_M417_Wrapper_C", "M417"},
         {"BP_Other_HuntingBow_Wrapper_C", "爆炸弓"},
         {"BP_Sniper_Mini14_Wrapper_C", "Mini14"}}},
    {9, {// 配件
         {"QK_Large_Suppressor", "步消"},
         {"BP_QK_Choke_Pickup_C", "11"}}},
    {10, {// 地铁宝箱
          {"EscapeBox_SupplyBox_", "物资箱"},
          {"EscapeBoxHight_SupplyBox_", "物资箱"}}}};
bool isPartialMatchedType(int type, const std::string &itemName, std::string &matchedName)
{
    auto it = itemTypeMap.find(type);
    if (it != itemTypeMap.end())
    {
        auto &subMap = it->second;
        for (const auto &entry : subMap)
        {
            if (itemName.find(entry.first) != std::string::npos)
            {
                matchedName = entry.second;
                return true;
            }
        }
    }
    return false;
}
void ImGuiELGS::WeaponPressureGunValue(int weaponIndex, float Fov, bool &isShotgun, bool &isSniper, float &weaponPressure, float &submachinePrediction)
{
    // 喷子武器ID列表
    const std::unordered_set<int> shotgunWeaponIDs = {104001, 104002, 104003, 104004, 104100, 104101, 104102};

    // 狙击武器ID列表
    const std::unordered_set<int> sniperWeaponIDs = {103001, 103002, 103003, 103004, 103006, 103009, 103011, 103012, 103013, 103014, 103015, 103016, 103100, 103903};

    // 冲锋枪武器ID列表
    const std::unordered_set<int> submachineWeaponIDs = {102001, 102002, 102003, 102004, 102005, 102007, 102009};

    // 基础武器 ID 到地铁武器 ID 集合的映射
    const std::unordered_map<int, std::unordered_set<int>> metroWeaponToBaseWeaponMap = {
        {102003, {9811015, 9811057, 9811066, 9811058, 9811021, 9811020, 9811019}},
        {102007, {9811036, 9811040, 9811041, 9811042}},
        {101014, {9812143}},
        {102008, {9811043, 9811047, 9811048, 9811049}},
        {102105, {9811050, 9811065, 9811061, 9811068, 9811062, 9811054, 9811055, 9811056}},
        {101001, {9812002, 9812006, 9812007, 9812123, 9812008}},
        {101003, {9812015, 9812124, 9812019, 9812020, 9812021}},
        {101004, {9812023, 9812125, 9812027, 9812028, 9812029, 9812092}},
        {101005, {9812029, 9812116, 9812099, 9812126, 9812100, 9812032, 9812033, 9812034}},
        {101006, {9812036, 9812127, 9812039, 9812101, 9812117, 9812102, 9812040, 9812041, 9812042}},
        {101007, {9812043, 9812091, 9812103, 9812104, 9812047, 9812048, 9812049}},
        {101008, {9812050, 9812105, 9812129, 9812106, 9812054, 9812055, 9812056}},
        {101010, {9812064, 9812068, 9812069, 9812070}},
        {101012, {9812078, 9812131, 9812109, 9812110, 9812082, 9812083, 9812084}},
        {101013, {9812085, 9812143, 9812089, 9812090, 9812091}},
        {105001, {9813001, 9813035, 9813036, 9813004, 9813005, 9813006}},
        {105010, {9813022, 9813050, 9813053, 9813037, 9813038, 9813025, 9813026, 9813027}},
        {105012, {9813029, 9813054, 9813039, 9813051, 9813040, 9813032, 9813033, 9813034}},
        {103002, {9814008}},
        {103003, {9814015, 9814060, 9814061}},
        {103012, {9814036, 9814062, 9814063}},
        {103015, {9814043, 9814064, 9814065}},
        {103004, {9815001, 9815072, 9815073}},
        {103007, {9815022, 9815074, 9815085, 9815075}},
        {103009, {9815029, 9815076, 9815077}},
        {105013, {9813055, 9813056}},
        {103013, {9815043, 9815078, 9815087, 9815079}},
        {103014, {9815050, 9815080, 9815088, 9815081}},
        {103100, {9815064, 9815082, 9815083}}};

    // 默认返回值
    isShotgun = false;
    isSniper = false;
    weaponPressure = 0.75f; // 默认压力值

    const float epsilon = 0.01f; // 定义一个小误差范围
    int switchIndex = (Fov > 75 && Fov <= 130) ? 1 : (Fov == 70 || Fov == 75)         ? 2
                                                 : (Fov == 55 || Fov == 60)           ? 3
                                                 : ((int)Fov == 44)                   ? 4
                                                 : ((int)Fov == 26)                   ? 5
                                                 : ((int)Fov == 20)                   ? 6
                                                 : ((int)Fov == 13)                   ? 7
                                                 : (std::abs(Fov - 11.03f) < epsilon) ? 8
                                                                                      : -1;

    submachinePrediction = (submachineWeaponIDs.find(weaponIndex) != submachineWeaponIDs.end()) ? 0.03f : 0.0f;
    // 检查是否为喷子
    isShotgun = (shotgunWeaponIDs.find(weaponIndex) != shotgunWeaponIDs.end());
    // 检查是否为狙击枪
    isSniper = (sniperWeaponIDs.find(weaponIndex) != sniperWeaponIDs.end());

    // 检查地铁武器映射
    for (const auto &[baseWeapon, metroWeapons] : metroWeaponToBaseWeaponMap)
    {
        if (metroWeapons.find(weaponIndex) != metroWeapons.end())
        {
            // 如果当前武器是地铁武器，则映射到基础武器 ID
            weaponIndex = baseWeapon;
            break;
        }
    }
    // 获取枪械力度和 Fov 系数
    static const std::unordered_map<int, int> weaponFovIndexMap = {
        {101001, 60}, // AKM
        {101002, 61}, // M16A4
        {101003, 62}, // SCAR-L
        {101004, 63}, // M416
        {101005, 64}, // Groza
        {101006, 65}, // AUG
        {101007, 66}, // QBZ
        {101008, 67}, // M762
        {101009, 68}, // Mk47
        {101010, 69}, // G36C
        {101011, 70}, // AC-VAL
        {101012, 71}, // 蜜獾突击步枪
        {101013, 72}, // 法玛斯
        {102001, 73}, // UZI
        {102002, 74}, // UMP 5
        {102003, 75}, // Vector
        {102004, 76}, // 汤姆逊
        {102005, 77}, // 野牛
        {102007, 78}, // MP5K
        {102105, 79}, // P90
        {105001, 80}, // M249
        {105002, 81}, // DP-28
        {105010, 82}, // MG3
        {105012, 83}, // PKM
        {105013, 84}, // MG-36
        {102008, 85}, // AKS
        {103007, 86}, // MK14
        {101014, 87}, // ACE32
        {102009, 88}, // JS9
        {103006, 89}, // mini4
        {103009, 90}, // slr
        {103004, 91}, // sks
        {103100, 92}, // mk12
        {103013, 93}, // m417
        {103014, 94}, // mk20
        {103010, 95}, // QBU
        {101016, 96}  // ARX
    };
    // 获取枪械 Fov 系数
    auto it = weaponFovIndexMap.find(weaponIndex);
    if (it != weaponFovIndexMap.end() && switchIndex != -1)
    {
        int FovIndex = it->second;

        // 结合 Fov 系数
        weaponPressure = imguiswitch_information.floatswitch[FovIndex] * imguiswitch_information.floatswitch[switchIndex];
    }
}

void ImGuiELGS::GetVehiclePressure(const std::string &vehicleName, float &pressure)
{
    const std::unordered_map<std::string, float *> VehiclePressureMap = {
        {"Mirado", &imguiswitch_information.floatswitch[9]},
        {"VH_Horse", &imguiswitch_information.floatswitch[10]},
        {"BP_VH_Buggy", &imguiswitch_information.floatswitch[11]},
        {"VH_Dacia_", &imguiswitch_information.floatswitch[12]},
        {"VH_UAZ01_New_C", &imguiswitch_information.floatswitch[13]},
        {"CoupeRB", &imguiswitch_information.floatswitch[14]},
        {"PG117", &imguiswitch_information.floatswitch[15]},
        {"VH_4SportCar_C", &imguiswitch_information.floatswitch[16]},
        {"VH_Kite_C", &imguiswitch_information.floatswitch[17]},
        {"VH_Drift", &imguiswitch_information.floatswitch[18]},
        {"VH_Blanc_C", &imguiswitch_information.floatswitch[19]}};

    float defaultPressure = 0.65f; // 默认压力值也使用可配置值
    pressure = defaultPressure;
    for (const auto &[key, value] : VehiclePressureMap)
    {
        if (vehicleName.find(key) != std::string::npos)
        { // 在完整类名中查找关键字
            pressure = *value;
            return;
        }
    }
}

ImColor tempColors[] = {
    ImColor(255, 0, 0, 255), // 红色
    ImColor(0, 255, 0, 255), // 绿色
    ImColor(0, 0, 255, 255), // 蓝色
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255),
    ImColor(0, 255, 0, 255)};

void miaobian(float size, int x, int y, ImVec4 color, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 1.0, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y + 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y - 1.0), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}
void 绘制加粗文本(float size, float x, float y, ImColor color, ImColor color1, const char *str)
{
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x - 0.8, y - 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x + 0.8, y + 0.8), color1, str);
    ImGui::GetBackgroundDrawList()->AddText(NULL, size, ImVec2(x, y), color, str);
}

void DrawPlayerBox(ImDrawList *Draw, float left, float right, float bottom, float top, float x, float y, ImColor color, float size)
{
    float LineSize = size;
    // x距离，y距离
    float xd = x - left;
    float yd = y - top;

    // 左上角
    Draw->AddLine(ImVec2(left, top), ImVec2(left, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, top), ImVec2(left + xd / 2, top), color, LineSize);

    // 右上角
    Draw->AddLine(ImVec2(right, top), ImVec2(right, top + yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, top), ImVec2(right - xd / 2, top), color, LineSize);

    // 左下角
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(left, bottom), ImVec2(left + xd / 2, bottom), color, LineSize);

    // 右下角
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right, bottom - yd / 3), color, LineSize);
    Draw->AddLine(ImVec2(right, bottom), ImVec2(right - xd / 2, bottom), color, LineSize);
}
ImTextureID ImAgeHeadFile(const unsigned char *buf, int len)
{
    int w, h, n;
    stbi_uc *data = stbi_png_load_from_memory(buf, len, &w, &h, &n, 0);
    GLuint texture;
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, data);
    }
    else
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    }
    stbi_image_free(data);
    ImTextureID image_id = (ImTextureID)(GLuint *)texture;
    return image_id;
}

bool 获取枪械信息(int 枪械编码, ImTextureID *图片名称)
{
    auto it = 手持图片.find(枪械编码);
    if (it != 手持图片.end())
    {
        *图片名称 = it->second;
        return true;
    }
    return false;
}

void 加载图片()
{
    手持图片[101001] = ImAgeHeadFile(picture_101001_png, sizeof(picture_101001_png));
    手持图片[101002] = ImAgeHeadFile(picture_101002_png, sizeof(picture_101002_png));
    手持图片[101003] = ImAgeHeadFile(picture_101003_png, sizeof(picture_101003_png));
    手持图片[101004] = ImAgeHeadFile(picture_101004_png, sizeof(picture_101004_png));
    手持图片[101005] = ImAgeHeadFile(picture_101005_png, sizeof(picture_101005_png));
    手持图片[101006] = ImAgeHeadFile(picture_101006_png, sizeof(picture_101006_png));
    手持图片[101007] = ImAgeHeadFile(picture_101007_png, sizeof(picture_101007_png));
    手持图片[101008] = ImAgeHeadFile(picture_101008_png, sizeof(picture_101008_png));
    手持图片[101009] = ImAgeHeadFile(picture_101009_png, sizeof(picture_101009_png));
    手持图片[101010] = ImAgeHeadFile(picture_101010_png, sizeof(picture_101010_png));
    手持图片[101011] = ImAgeHeadFile(picture_101011_png, sizeof(picture_101011_png));
    手持图片[101012] = ImAgeHeadFile(picture_101012_png, sizeof(picture_101012_png));
    手持图片[101013] = ImAgeHeadFile(picture_101013_png, sizeof(picture_101013_png));
    手持图片[102001] = ImAgeHeadFile(picture_102001_png, sizeof(picture_102001_png));
    手持图片[102002] = ImAgeHeadFile(picture_102002_png, sizeof(picture_102002_png));
    手持图片[102003] = ImAgeHeadFile(picture_102003_png, sizeof(picture_102003_png));
    手持图片[102004] = ImAgeHeadFile(picture_102004_png, sizeof(picture_102004_png));
    手持图片[102005] = ImAgeHeadFile(picture_102005_png, sizeof(picture_102005_png));
    手持图片[102007] = ImAgeHeadFile(picture_102007_png, sizeof(picture_102007_png));
    手持图片[102008] = ImAgeHeadFile(picture_102008_png, sizeof(picture_102008_png));
    手持图片[102105] = ImAgeHeadFile(picture_102105_png, sizeof(picture_102105_png));
    手持图片[103001] = ImAgeHeadFile(picture_103001_png, sizeof(picture_103001_png));
    手持图片[103002] = ImAgeHeadFile(picture_103002_png, sizeof(picture_103002_png));
    手持图片[103003] = ImAgeHeadFile(picture_103003_png, sizeof(picture_103003_png));
    手持图片[103004] = ImAgeHeadFile(picture_103004_png, sizeof(picture_103004_png));
    手持图片[103005] = ImAgeHeadFile(picture_103005_png, sizeof(picture_103005_png));
    手持图片[103006] = ImAgeHeadFile(picture_103006_png, sizeof(picture_103006_png));
    手持图片[103007] = ImAgeHeadFile(picture_103007_png, sizeof(picture_103007_png));
    手持图片[103008] = ImAgeHeadFile(picture_103008_png, sizeof(picture_103008_png));
    手持图片[103009] = ImAgeHeadFile(picture_103009_png, sizeof(picture_103009_png));
    手持图片[103010] = ImAgeHeadFile(picture_103010_png, sizeof(picture_103010_png));
    手持图片[103011] = ImAgeHeadFile(picture_103011_png, sizeof(picture_103011_png));
    手持图片[103012] = ImAgeHeadFile(picture_103012_png, sizeof(picture_103012_png));
    手持图片[103013] = ImAgeHeadFile(picture_103013_png, sizeof(picture_103013_png));
    手持图片[103014] = ImAgeHeadFile(picture_103014_png, sizeof(picture_103014_png));
    手持图片[103015] = ImAgeHeadFile(picture_103015_png, sizeof(picture_103015_png));
    手持图片[103016] = ImAgeHeadFile(picture_103016_png, sizeof(picture_103016_png));
    手持图片[103100] = ImAgeHeadFile(picture_103100_png, sizeof(picture_103100_png));
    手持图片[103901] = ImAgeHeadFile(picture_103901_png, sizeof(picture_103901_png));
    手持图片[103902] = ImAgeHeadFile(picture_103902_png, sizeof(picture_103902_png));
    手持图片[103903] = ImAgeHeadFile(picture_103903_png, sizeof(picture_103903_png));
    手持图片[104001] = ImAgeHeadFile(picture_104001_png, sizeof(picture_104001_png));
    手持图片[104002] = ImAgeHeadFile(picture_104002_png, sizeof(picture_104002_png));
    手持图片[104003] = ImAgeHeadFile(picture_104003_png, sizeof(picture_104003_png));
    手持图片[104004] = ImAgeHeadFile(picture_104004_png, sizeof(picture_104004_png));
    手持图片[104005] = ImAgeHeadFile(picture_104005_png, sizeof(picture_104005_png));
    手持图片[104100] = ImAgeHeadFile(picture_104100_png, sizeof(picture_104100_png));
    手持图片[105001] = ImAgeHeadFile(picture_105001_png, sizeof(picture_105001_png));
    手持图片[105002] = ImAgeHeadFile(picture_105002_png, sizeof(picture_105002_png));
    手持图片[105010] = ImAgeHeadFile(picture_105010_png, sizeof(picture_105010_png));
    手持图片[105012] = ImAgeHeadFile(picture_105012_png, sizeof(picture_105012_png));
    手持图片[105013] = ImAgeHeadFile(picture_105013_png, sizeof(picture_105013_png));
    手持图片[106001] = ImAgeHeadFile(picture_106001_png, sizeof(picture_106001_png));
    手持图片[106002] = ImAgeHeadFile(picture_106002_png, sizeof(picture_106002_png));
    手持图片[106003] = ImAgeHeadFile(picture_106003_png, sizeof(picture_106003_png));
    手持图片[106004] = ImAgeHeadFile(picture_106004_png, sizeof(picture_106004_png));
    手持图片[106005] = ImAgeHeadFile(picture_106005_png, sizeof(picture_106005_png));
    手持图片[106006] = ImAgeHeadFile(picture_106006_png, sizeof(picture_106006_png));
    手持图片[106007] = ImAgeHeadFile(picture_106007_png, sizeof(picture_106007_png));
    手持图片[106008] = ImAgeHeadFile(picture_106008_png, sizeof(picture_106008_png));
    手持图片[106010] = ImAgeHeadFile(picture_106010_png, sizeof(picture_106010_png));
    手持图片[107001] = ImAgeHeadFile(picture_107001_png, sizeof(picture_107001_png));
    手持图片[107007] = ImAgeHeadFile(picture_107007_png, sizeof(picture_107007_png));
    手持图片[107010] = ImAgeHeadFile(picture_107010_png, sizeof(picture_107010_png));
    手持图片[107909] = ImAgeHeadFile(picture_107909_png, sizeof(picture_107909_png));
    手持图片[108001] = ImAgeHeadFile(picture_108001_png, sizeof(picture_108001_png));
    手持图片[108002] = ImAgeHeadFile(picture_108002_png, sizeof(picture_108002_png));
    手持图片[108003] = ImAgeHeadFile(picture_108003_png, sizeof(picture_108003_png));
    手持图片[108004] = ImAgeHeadFile(picture_108004_png, sizeof(picture_108004_png));
    手持图片[602001] = ImAgeHeadFile(picture_602001_png, sizeof(picture_602001_png));
    手持图片[602002] = ImAgeHeadFile(picture_602002_png, sizeof(picture_602002_png));
    手持图片[602003] = ImAgeHeadFile(picture_602003_png, sizeof(picture_602003_png));
    手持图片[602004] = ImAgeHeadFile(picture_602004_png, sizeof(picture_602004_png));
    手持图片[602075] = ImAgeHeadFile(picture_602075_png, sizeof(picture_602075_png));
    手持图片[107910] = ImAgeHeadFile(picture_107910_png, sizeof(picture_107910_png));

    手持图片[101014] = ImAgeHeadFile(picture_101014_png, sizeof(picture_101014_png));
    手持图片[102009] = ImAgeHeadFile(picture_102009_png, sizeof(picture_102009_png));
    手持图片[107100] = ImAgeHeadFile(picture_107100_png, sizeof(picture_107100_png));

    手持图片[106013] = ImAgeHeadFile(picture_106013_png, sizeof(picture_106013_png));
    手持图片[103101] = ImAgeHeadFile(picture_103101_png, sizeof(picture_103101_png));

    手持图片[107006] = ImAgeHeadFile(picture_107006_png, sizeof(picture_107006_png));
    手持图片[107008] = ImAgeHeadFile(picture_107008_png, sizeof(picture_107008_png));

    手持图片[103017] = ImAgeHeadFile(picture_103017_png, sizeof(picture_103017_png));
    手持图片[101016] = ImAgeHeadFile(picture_101016_png, sizeof(picture_101016_png));

    // 手持图片映射
    手持图片[9811015] = 手持图片[102003];
    手持图片[9811057] = 手持图片[102003];
    手持图片[9811058] = 手持图片[102003];
    手持图片[9812130] = 手持图片[101010];
    手持图片[9812125] = 手持图片[101004];
    手持图片[9812126] = 手持图片[101005];
    手持图片[9812127] = 手持图片[101006];
    手持图片[9812124] = 手持图片[101003];
    手持图片[9812131] = 手持图片[101012];
    手持图片[9812143] = 手持图片[101013];
    手持图片[9812129] = 手持图片[101008];
    手持图片[9815094] = 手持图片[103014];
    手持图片[9815091] = 手持图片[103007];
    手持图片[9815092] = 手持图片[103009];
    手持图片[9815090] = 手持图片[103004];
    手持图片[9811067] = 手持图片[102007];
    手持图片[9811068] = 手持图片[102105];
    手持图片[9811066] = 手持图片[102003];
    手持图片[9813056] = 手持图片[105013];
    手持图片[9813053] = 手持图片[105010];
    手持图片[9813054] = 手持图片[105012];
    手持图片[9814074] = 手持图片[103012];

    手持图片[9811036] = 手持图片[102007];

    手持图片[9811043] = 手持图片[102008];
    手持图片[9812143] = 手持图片[101014];

    手持图片[9811050] = 手持图片[102105];
    手持图片[9811061] = 手持图片[102105];
    手持图片[9811062] = 手持图片[102105];

    手持图片[9812002] = 手持图片[101001];
    手持图片[9812123] = 手持图片[101001];

    手持图片[9812015] = 手持图片[101003];

    手持图片[9812023] = 手持图片[101004];

    手持图片[9812029] = 手持图片[101005];
    手持图片[9812099] = 手持图片[101005];
    手持图片[9812100] = 手持图片[101005];

    手持图片[9812036] = 手持图片[101006];
    手持图片[9812101] = 手持图片[101006];
    手持图片[9812102] = 手持图片[101006];

    手持图片[9812043] = 手持图片[101007];
    手持图片[9812103] = 手持图片[101007];
    手持图片[9812104] = 手持图片[101007];

    手持图片[9812050] = 手持图片[101008];
    手持图片[9812105] = 手持图片[101008];
    手持图片[9812106] = 手持图片[101008];

    手持图片[9812064] = 手持图片[101010];
    手持图片[9812107] = 手持图片[101010];
    手持图片[9812108] = 手持图片[101010];

    手持图片[9812078] = 手持图片[101012];
    手持图片[9812109] = 手持图片[101012];
    手持图片[9812110] = 手持图片[101012];

    手持图片[9812085] = 手持图片[101013];
    手持图片[9812111] = 手持图片[101013];
    手持图片[9812112] = 手持图片[101013];

    手持图片[9813001] = 手持图片[105001];
    手持图片[9813035] = 手持图片[105001];
    手持图片[9813036] = 手持图片[105001];

    手持图片[9813022] = 手持图片[105010];
    手持图片[9813037] = 手持图片[105010];
    手持图片[9813038] = 手持图片[105010];

    手持图片[9813029] = 手持图片[105012];
    手持图片[9813039] = 手持图片[105012];
    手持图片[9813040] = 手持图片[105012];

    手持图片[9814008] = 手持图片[103002];

    手持图片[9814015] = 手持图片[103003];
    手持图片[9814060] = 手持图片[103003];
    手持图片[9814061] = 手持图片[103003];

    手持图片[9814036] = 手持图片[103012];
    手持图片[9814062] = 手持图片[103012];
    手持图片[9814063] = 手持图片[103012];
    手持图片[103904] = 手持图片[103012];

    手持图片[9814043] = 手持图片[103015];
    手持图片[9814064] = 手持图片[103015];
    手持图片[9814065] = 手持图片[103015];

    手持图片[9815001] = 手持图片[103004];
    手持图片[9815072] = 手持图片[103004];
    手持图片[9815073] = 手持图片[103004];

    手持图片[9815022] = 手持图片[103007];
    手持图片[9815074] = 手持图片[103007];
    手持图片[9815075] = 手持图片[103007];

    手持图片[9815029] = 手持图片[103009];
    手持图片[9815076] = 手持图片[103009];
    手持图片[9815077] = 手持图片[103009];

    手持图片[9815043] = 手持图片[103013];
    手持图片[9815078] = 手持图片[103013];
    手持图片[9815079] = 手持图片[103013];

    手持图片[9815050] = 手持图片[103014];
    手持图片[9815080] = 手持图片[103014];
    手持图片[9815081] = 手持图片[103014];

    手持图片[9815064] = 手持图片[103100];
    手持图片[9815082] = 手持图片[103100];
    手持图片[9815083] = 手持图片[103100];
    手持图片[9825001] = 手持图片[602001];
    手持图片[9825002] = 手持图片[602002];
    手持图片[9825003] = 手持图片[602003];
    手持图片[9825004] = 手持图片[602004];
    手持图片[9826001] = 手持图片[602004];
    手持图片[9826002] = 手持图片[602004];
    手持图片[9810015] = 手持图片[104003];
    手持图片[9810025] = 手持图片[104004];
    手持图片[9810032] = 手持图片[104100];
    手持图片[9810043] = 手持图片[104005];
    手持图片[602104] = 手持图片[108001];
}

void ImGuiELGS::InitializeFoundationConfiGuration()
{
    OneTimeFrame = 90;

    touch_information.TouchRadius = 0.06f;
    touch_information.TouchPoints.x = 0.8f;
    touch_information.TouchPoints.y = 0.6f;

    touch_information.floatswitch[0] = 900.f;
    touch_information.floatswitch[1] = 0.05f;
    touch_information.floatswitch[2] = 0.10f;
    touch_information.floatswitch[3] = 0.10f;
    imguiswitch_information.intswitch[50] = 0;
    imguiswitch_information.intswitch[51] = 0;
    imguiswitch_information.intswitch[52] = 0;
    imguiswitch_information.intswitch[53] = 0;
    imguiswitch_information.intswitch[54] = 0;
    imguiswitch_information.intswitch[90] = 200;

    imguiswitch_information.floatswitch[30] = 100.f;
    imguiswitch_information.floatswitch[40] = -493.f;
    imguiswitch_information.floatswitch[41] = -126.f;
    imguiswitch_information.floatswitch[42] = 100.f;
    imguiswitch_information.floatswitch[43] = 134.12f;
    imguiswitch_information.floatswitch[50] = 150.f;
    imguiswitch_information.floatswitch[51] = 200.f;
    imguiswitch_information.floatswitch[52] = 0.13f;
    imguiswitch_information.floatswitch[53] = 1.5f;
    imguiswitch_information.floatswitch[54] = 1.3f;
    imguiswitch_information.floatswitch[55] = 1.0f;

    imguiswitch_information.floatswitch[90] = 0.9f;
    imguiswitch_information.floatswitch[91] = 1.5f;
    imguiswitch_information.floatswitch[92] = 1.5f;
    imguiswitch_information.floatswitch[93] = 1.5f;
    imguiswitch_information.floatswitch[94] = 1.5f;
    imguiswitch_information.floatswitch[95] = 1.5f;
    imguiswitch_information.floatswitch[96] = 1.5f;
    ycsz = 2.05f;
    ycsd = 1.83f;
    ycsp = 1.0f;
    imguiswitch_information.floatswitch[56] = 500.f;
    imguiswitch_information.floatswitch[57] = 1.70f;
    imguiswitch_information.colorswitch[0] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[1] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[2] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[3] = ImColor(255, 50, 50, 200);   // 真人
    imguiswitch_information.colorswitch[4] = ImColor(230, 230, 50, 200);  // 掩体
    imguiswitch_information.colorswitch[5] = ImColor(50, 255, 50, 200);   // 人机
    imguiswitch_information.colorswitch[6] = ImColor(200, 200, 200, 200); // 人机
    imguiswitch_information.colorswitch[7] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[8] = ImColor(255, 255, 255, 255);
    imguiswitch_information.colorswitch[9] = ImColor(255, 255, 255, 255);
}

VecTor2 ImGuiELGS::GetTouchScreenDimension(int Handle)
{
    int TouchScreenX[6] = {0};
    int TouchScreenY[6] = {0};
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_X), TouchScreenX);
    ioctl(Handle, EVIOCGABS(ABS_MT_POSITION_Y), TouchScreenY);
    return VecTor2(TouchScreenX[2], TouchScreenY[2]);
}

ImTextureID ImGuiELGS::texturereadsfile(const unsigned char *buffer, int length)
{
    int w = 0, h = 0, n = 0;
    GLuint texture;

    // 加载图片数据
    stbi_uc *bufferdata = stbi_png_load_from_memory(buffer, length, &w, &h, &n, 0);
    if (!bufferdata)
    {
        return nullptr;
    }

    // 生成纹理ID并设置纹理参数
    glGenTextures(1, &texture);
    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);

    // 根据通道数判断是否为灰度、RGB或RGBA
    if (n == 1)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_LUMINANCE, w, h, 0, GL_LUMINANCE, GL_UNSIGNED_BYTE, bufferdata);
    }
    else if (n == 3)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, w, h, 0, GL_RGB, GL_UNSIGNED_BYTE, bufferdata);
    }
    else if (n == 4)
    {
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, w, h, 0, GL_RGBA, GL_UNSIGNED_BYTE, bufferdata);
    }
    else
    {
        stbi_image_free(bufferdata);
        return nullptr;
    }

    // 释放图像数据
    stbi_image_free(bufferdata);

    // 返回生成的纹理ID
    return reinterpret_cast<ImTextureID>(static_cast<uintptr_t>(texture));
}

string ImGuiELGS::GetTouchScreenDeviceFile()
{
    const char *command = "getevent";
    FILE *PIPE = popen(command, "r");
    if (!PIPE)
    {
        return "";
    }
    int lineCount = 0;
    string result = "";
    char buffer[128] = "";
    cout << "\033[35;1m[+] 请滑动一下屏幕\033[30;1m" << endl;
    while (!feof(PIPE))
    {
        if (fgets(buffer, 128, PIPE) != NULL)
        {
            string line(buffer);
            if (line.find("/dev/input/event") != string::npos)
            {
                result += line;
                lineCount++;
            }
            if (lineCount == 20)
            {
                break;
            }
        }
    }
    pclose(PIPE);
    regex pattern("/dev/input/event\\d+");
    sregex_iterator iter(result.begin(), result.end(), pattern);
    sregex_iterator end;
    string lastEventLine = "";
    while (iter != end)
    {
        lastEventLine = iter->str();
        ++iter;
    }
    return lastEventLine;
}

bool ImGuiELGS::ImGuiGetSurfaceWindow()
{
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    if (output == 1)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Canary", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x40);
    }
    else if (output == 2)
    {
        native_window = Android::NativeWindowCreator::CreateSurfaceNativeWindow("Canary", resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth, 0x00);
    }
    else
    {
        cout << "\033[31;1m[+] 输入错误\033[30;1m" << endl;
        return false;
    }
    ANativeWindow_acquire(native_window);
    display = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    if (display == EGL_NO_DISPLAY)
    {
        return false;
    }
    if (eglInitialize(display, 0, 0) != EGL_TRUE)
    {
        return false;
    }
    EGLint num_config = 0;
    const EGLint attribList[] = {EGL_SURFACE_TYPE, EGL_WINDOW_BIT, EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT, EGL_BLUE_SIZE, 8, EGL_GREEN_SIZE, 8, EGL_RED_SIZE, 8, EGL_ALPHA_SIZE, 8, EGL_BUFFER_SIZE, 32, EGL_DEPTH_SIZE, 24, EGL_STENCIL_SIZE, 8, EGL_NONE};
    if (eglChooseConfig(display, attribList, nullptr, 0, &num_config) != EGL_TRUE)
    {
        return false;
    }
    if (!eglChooseConfig(display, attribList, &config, 1, &num_config))
    {
        return false;
    }
    EGLint egl_format;
    eglGetConfigAttrib(display, config, EGL_NATIVE_VISUAL_ID, &egl_format);
    ANativeWindow_setBuffersGeometry(native_window, 0, 0, egl_format);
    const EGLint attrib_list[] = {EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE};
    context = eglCreateContext(display, config, EGL_NO_CONTEXT, attrib_list);
    if (context == EGL_NO_CONTEXT)
    {
        return false;
    }
    surface = eglCreateWindowSurface(display, config, native_window, nullptr);
    if (surface == EGL_NO_SURFACE)
    {
        return false;
    }
    if (!eglMakeCurrent(display, surface, surface, context))
    {
        return false;
    }
    return true;
}

void ImGuiELGS::ImGuiGetScreenInformation()
{
    touch_information.TouchDeviceFile = open(GetTouchScreenDeviceFile().c_str(), O_RDONLY | O_SYNC | O_NONBLOCK);
    if (touch_information.TouchDeviceFile <= 0)
    {
        cout << "\033[31;1m[-] 获取触摸设备文件失败\033[30;1m" << endl;
        return;
    }
    else
    {
        touch_information.TouchScreenSize = GetTouchScreenDimension(touch_information.TouchDeviceFile);
    }
    thread *screeninformationthread = new thread([this]
                                                 {
		static int ORIENTATIONTEMP = 0;
        for (;;) {
			displayinformation = Android::NativeWindowCreator::GetDisplayInfo();
			if (!ORIENTATIONTEMP) {
				resolution_information.Orientation = displayinformation.orientation;
				ORIENTATIONTEMP = 1;
			}
			resolution_information.Width = displayinformation.upwidth / 2;
			resolution_information.Heiht = displayinformation.upheight / 2;
			resolution_information.ScreenWidth = displayinformation.upwidth;
			resolution_information.ScreenHeiht = displayinformation.upheight;
			resolution_information.FixedScreenWidth = displayinformation.width;
			resolution_information.FixedScreenHeiht = displayinformation.height;
			if (displayinformation.orientation != resolution_information.Orientation) {
				touch_information.TouchOrientationControl = true;
				resolution_information.Orientation = displayinformation.orientation;
			}
			this_thread::sleep_for(0.1s);
        } });
    screeninformationthread->detach();
    return;
}

void ImGuiELGS::ImGuiWindowStar()
{
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(resolution_information.FixedScreenWidth + resolution_information.FixedScreenHeiht, resolution_information.FixedScreenHeiht + resolution_information.FixedScreenWidth);
    ImGui::NewFrame();
}

void ImGuiELGS::ImGuiWindowExit()
{
    glViewport(0, 0, ImGui::GetIO().DisplaySize.x, ImGui::GetIO().DisplaySize.y);
    glClearColor(0, 0, 0, 0);
    glClear(GL_COLOR_BUFFER_BIT);
    glFlush();
    if (display == EGL_NO_DISPLAY)
    {
        return;
    }
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    eglSwapBuffers(display, surface);
}

void ImGuiELGS::ImGuiWindowRele()
{
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplAndroid_Shutdown();
    ImGui::DestroyContext();
    if (display != EGL_NO_DISPLAY)
    {
        eglMakeCurrent(display, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        if (context != EGL_NO_CONTEXT)
        {
            eglDestroyContext(display, context);
        }
        if (surface != EGL_NO_SURFACE)
        {
            eglDestroySurface(display, surface);
        }
        eglTerminate(display);
    }
    display = EGL_NO_DISPLAY;
    context = EGL_NO_CONTEXT;
    surface = EGL_NO_SURFACE;
    ANativeWindow_release(native_window);
}

void ImGuiELGS::ImGuiInItialization()
{
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO &io = ImGui::GetIO();
    io.FontGlobalScale = 1.1f;

    // 使用简洁的亮色主题
    ImGui::StyleColorsLight();
    ImGuiStyle &Style = ImGui::GetStyle();

    // 调整样式参数以简化界面
    Style.WindowPadding = ImVec2(8.0f, 8.0f);
    Style.ItemSpacing = ImVec2(6.0f, 3.0f);
    Style.GrabMinSize = 10.0f;
    Style.WindowRounding = 10.0f;
    Style.FrameBorderSize = 0.5f;
    Style.FrameRounding = 2.0f;
    Style.GrabRounding = 2.0f;
    Style.ScrollbarSize = 30.0f;
    Style.WindowBorderSize = 0.5f;

    // 其他初始化
    InitializeFoundationConfiGuration();
    ImGui_ImplAndroid_Init(native_window);
    ImGui_ImplOpenGL3_Init("#version 300 es");

    // 字体加载
    io.Fonts->AddFontFromMemoryTTF((void *)FontFile, Fontsize, 28.f, NULL, io.Fonts->GetGlyphRangesChineseFull());
    io.Fonts->AddFontFromMemoryTTF((void *)icons_binary, sizeof(icons_binary), 34.f);
    io.Fonts->AddFontFromMemoryTTF((void *)font_bold_binary, sizeof(font_bold_binary), 34.f);
    IM_ASSERT(io.Fonts != NULL);

    LoadFile(".HP_SIVE");
    加载图片();
}
bool ImGuiELGS::InitializeDrawing()
{
    GetPid("com.tencent.tmgp.pubgmhd");
    initialize();
    ModulesBase[0] = GetModuleAddressTwo("libUE4.so");
    // printf("base = %lX\n", ModulesBase[0]);
    return true;
}
