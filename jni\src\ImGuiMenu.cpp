#include "ImGuiELGS.h"
#include "Custom.h"
extern char buffer[80];
void ImGuiELGS::ImGuiWindowMenu()
{
	if (imguiswitch_information.boolswitch[11] && initializeaimi)
	{
		static bool AimiDown = false;
		static ImVec2 AimiPos = {0, 0};
		if (ImGui::Begin("Aimi", &initializeaimi, ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoBackground))
		{
			if (ImGui::IsItemActive())
			{
				if (!AimiDown)
				{
					AimiDown = true;
					AimiPos = ImGui::GetWindowPos();
				}
			}
			else if (AimiDown)
			{
				AimiDown = false;
				if (AimiPos.x == ImGui::GetWindowPos().x && AimiPos.y == ImGui::GetWindowPos().y)
				{
					imguiswitch_information.intswitch[59] = !imguiswitch_information.intswitch[59];
				}
			}
			if (imguiswitch_information.intswitch[59])
			{
				ImGui::Image(noaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
			else
			{
				ImGui::Image(yeaimi_icon, ImVec2{110, 110}, ImVec2{0, 0}, ImVec2{1, 1});
			}
		}
		ImGui::End();
	}
	ImVec2 WindowPos = ImVec2(1030.f, 780.f);
	ImGui::SetWindowSize("QC6.24秦川", WindowPos);
	CenterWindow.x = (resolution_information.ScreenWidth - WindowPos.x) / 2.f;
	CenterWindow.y = (resolution_information.ScreenHeiht - WindowPos.y) / 2.f;

	if (ImGuiWindowDisplay && ShutImGuiProcess)
	{
		if (ImGui::Begin("QC6.24秦川", &ImGuiWindowDisplay, ImGuiWindowFlags_NoDecoration))
		{
			static bool Navbar = true;
			static float Navbar_Width = 0;
			ImVec2 WindowPos = ImGui::GetWindowPos();
			Content_Anim = ImLerp(Content_Anim, 1.f, 0.04f);
			Navbar_Width = ImLerp(Navbar_Width, Navbar ? 0.f : 1.f, 0.04f);
			ImGui::PushItemFlag(ImGuiItemFlags_Disabled, !Navbar);
			ImGui::SetCursorPos({140.f, 25.f});
			ImGui::BeginGroup();
			ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[2]);
			ImGui::PushStyleColor(ImGuiCol_Text, ImGui::GetStyleColorVec4(ImGuiCol_Scheme));
			ImGui::Text("QC6.24");
			ImGui::PopStyleColor();
			ImGui::PopFont();
			if (ImGui::BeginChild("MainChild", {ImGui::GetCurrentWindow()->Size.x - 140.f, ImGui::GetCurrentWindow()->Size.y - ImGui::GetCurrentWindow()->DC.CursorPos.y + ImGui::GetCurrentWindow()->Pos.y}, false, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoScrollbar))
			{
				switch (Tab)
				{
				case 1:
				{
					if (ImGui::BeginChild("SubTab##1", {ImGui::GetWindowWidth() - 60.f, 90.f}, false))
					{
						if (Custom::subtab("QC6.24秦川##0", SubTab1 == 1))
							SubTab1 = 1;
					}
					ImGui::EndChild();
					if (ImGui::BeginChild("SubChild##1", {ImGui::GetWindowWidth(), -1}, false, ImGuiWindowFlags_NoBackground))
					{
						if (ImGui::BeginGroup())
						{
							switch (SubTab1)
							{
							case 1:
							{
								Custom::begin_child("QC6.24秦川", {(ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f, 0.f});
								{
									if (Pid > 0)
									{
										ImGui::Text("QC6.24秦川: %d", Pid);
									}
									ImGui::TextWrapped("       泄露传包直接格机");
									//ImGui::Text("这里显示倒计时 就是正版: %s", buffer);
									ImGui::Text("QC6.24秦川: %dx%d", resolution_information.ScreenWidth, resolution_information.ScreenHeiht);
									ImGui::Text("QC6.24秦川: %0.2fFPS %0.2fms", ImGui::GetIO().Framerate, 1000.f / ImGui::GetIO().Framerate);
									ImGui::Text("自身武器: %d, 自身动作: %d, 自身队伍: %d", 自身武器, 自身动作, OwnTeam);
									if (ImGui::SliderInt("帧数设置", &OneTimeFrame, 60, 144))
									{
										SaveFile(".HP_SIVE");
									}
									if (!initializedraw)
									{
										if (ImGui::Button("开启QC6.24秦川", {0.f, 60.f}))
										{
											initializedraw = InitializeDrawing();
										}
									}
									else
									{
										if (ImGui::Button("退出QC6.24秦川", {0.f, 60.f}))
										{
											ShutImGuiProcess = false;
										}
									}
								}
								Custom::end_child();
							}
							break;
							}
						}
						ImGui::EndGroup();
					}
					ImGui::EndChild();
				}
				break;
				case 2:
				{
					if (ImGui::BeginChild("Subtab##2", {ImGui::GetWindowWidth() - 60.f, 90.f}, false))
					{
						if (Custom::subtab("透视区域##0", SubTab2 == 1))
							SubTab2 = 1;
						ImGui::SameLine(0, 30);
						if (Custom::subtab("载具物资##1", SubTab2 == 2))
							SubTab2 = 2;
						ImGui::SameLine(0, 30);
						if (Custom::subtab("颜色设置##2", SubTab2 == 3))
							SubTab2 = 3;
					}
					ImGui::EndChild();
					if (ImGui::BeginChild("Main_Child##2", {ImGui::GetWindowWidth(), -1}, false, ImGuiWindowFlags_NoBackground))
					{
						if (ImGui::BeginGroup())
						{
							switch (SubTab2)
							{
							case 1:
							{
								Custom::begin_child("1", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Checkbox("忽略人机", &imguiswitch_information.boolswitch[10]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制骨骼", &imguiswitch_information.boolswitch[0]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制方框", &imguiswitch_information.boolswitch[1]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制射线", &imguiswitch_information.boolswitch[2]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制手持", &imguiswitch_information.boolswitch[9]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("2", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Checkbox("绘制雷达", &imguiswitch_information.boolswitch[3]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("防止偷袭", &imguiswitch_information.boolswitch[4]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制血条", &imguiswitch_information.boolswitch[6]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制距离", &imguiswitch_information.boolswitch[7]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("绘制名称", &imguiswitch_information.boolswitch[8]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("周围人数", &imguiswitch_information.boolswitch[30]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
							}
							break;
							case 2:
							{
								Custom::begin_child("1", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Checkbox("手雷预警", &imguiswitch_information.boolswitch[53]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("显示载具", &imguiswitch_information.boolswitch[54]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("头盔防具", &imguiswitch_information.boolswitch[55]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("牛逼道具", &imguiswitch_information.boolswitch[56]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("2", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Checkbox("死亡盒子", &imguiswitch_information.boolswitch[57]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("抗毒药品", &imguiswitch_information.boolswitch[58]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("喷子子弹", &imguiswitch_information.boolswitch[59]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("地铁箱子", &imguiswitch_information.boolswitch[60]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("别动这个", &imguiswitch_information.boolswitch[61]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
							}
							break;
							case 3:
							{
								Custom::begin_child("雷达设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Combo("血条UI", &imguiswitch_information.intswitch[1], "圆血条\0经典血条\0扁血条\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("线条粗细", &imguiswitch_information.floatswitch[57], 1.f, 5.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("雷达秦川调整", &imguiswitch_information.floatswitch[40], -1000.f, 1000.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("雷达上下调整", &imguiswitch_information.floatswitch[41], -1000.f, 1000.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("雷达缩放调整", &imguiswitch_information.floatswitch[42], 0.f, 200.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("雷达大小调整", &imguiswitch_information.floatswitch[43], 100.f, 300.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("颜色设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::ColorEdit4("方框颜色", imguiswitch_information.colorswitch[7], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("骨骼颜色", imguiswitch_information.colorswitch[8], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("射线颜色", imguiswitch_information.colorswitch[9], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("距离颜色", imguiswitch_information.colorswitch[0], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("名称颜色", imguiswitch_information.colorswitch[1], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("队标颜色", imguiswitch_information.colorswitch[2], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("人机颜色", imguiswitch_information.colorswitch[5], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::ColorEdit4("瞄准颜色", imguiswitch_information.colorswitch[6], ImGuiColorEditFlags_DisplayHex))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
							}
							break;
							}
						}
						ImGui::EndGroup();
					}
					ImGui::EndChild();
				}
				break;
				case 3:
				{
					if (ImGui::BeginChild("Subtab##3", {ImGui::GetWindowWidth() - 60.f, 90.f}, false))
					{
						if (Custom::subtab("自瞄设置##0", SubTab3 == 1))
							SubTab3 = 1;
						ImGui::SameLine(0, 30);
						if (Custom::subtab("速度距离##1", SubTab3 == 2))
							SubTab3 = 2;
						ImGui::SameLine(0, 30);
						if (Custom::subtab("压枪设置##2", SubTab3 == 3))
							SubTab3 = 3;
						ImGui::SameLine(0, 30);
						if (Custom::subtab("优先设置##3", SubTab3 == 4))
							SubTab3 = 4;
					}
					ImGui::EndChild();
					if (ImGui::BeginChild("Main_Child##3", {ImGui::GetWindowWidth(), -1}, false, ImGuiWindowFlags_NoBackground))
					{
						if (ImGui::BeginGroup())
						{
							switch (SubTab3)
							{
							case 1:
							{
								Custom::begin_child("触摸自瞄", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Checkbox("自瞄悬浮【绿色是开启】", &imguiswitch_information.boolswitch[11]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("喷子自瞄", &imguiswitch_information.boolswitch[48]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("连狙不瞄", &imguiswitch_information.boolswitch[49]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("显示瞄准变色", &imguiswitch_information.boolswitch[21]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("显示自瞄范围", &imguiswitch_information.boolswitch[51]))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("显示自瞄射线", &imguiswitch_information.boolswitch[52]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("触摸调整", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::SliderFloat("触摸位置左右", &touch_information.TouchPoints.y, 0.f, 1.f, "%.2f"))
									{
										touch_information.TouchOrientationControl = true;
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("触摸位置上下", &touch_information.TouchPoints.x, 0.f, 1.f, "%.2f"))
									{
										touch_information.TouchOrientationControl = true;
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("触摸范围调整", &touch_information.TouchRadius, 0.01f, 0.08f, "%.2f"))
									{
										touch_information.TouchOrientationControl = true;
										SaveFile(".HP_SIVE");
									}
									int sliderValue = static_cast<int>(touch_information.floatswitch[0]);
									if (ImGui::SliderInt("触摸帧数", &sliderValue, 400.f, 800.f, "%.2f"))
									{
										sliderValue = (sliderValue / 25) * 25;
										touch_information.floatswitch[0] = static_cast<float>(sliderValue);
										touch_information.TouchOrientationControl = true;
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Checkbox("显示触摸区域", &imguiswitch_information.boolswitch[50]))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
							}
							break;
							case 2:
							{
								Custom::begin_child("自瞄速度设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::SliderFloat("左右速度 跟不上就拉", &touch_information.floatswitch[1], 0.04f, 0.13f, "%.02f", 0.01f))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("压枪就0.10 不压就0.05", &touch_information.floatswitch[2], 0.05f, 0.10f, "%.02f", 0.01f))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("总速度 锁不死人就拉", &touch_information.floatswitch[3], 0.05f, 0.20f, "%.02f", 0.01f))
									{
										touch_information.TouchOrientationControl = true;
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("自瞄设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::SliderFloat("自瞄范围调整", &imguiswitch_information.floatswitch[50], 10.0f, 250.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("腰射", &imguiswitch_information.floatswitch[49], 10.f, 50.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("自瞄米数调整", &imguiswitch_information.floatswitch[51], 10.f, 300.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("自瞄预判调整", &imguiswitch_information.floatswitch[52], 1.2f, 2.0f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderInt("自瞄血量调整", &imguiswitch_information.intswitch[30], 0, 100))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("远处 圈大小 建议60", &imguiswitch_information.floatswitch[31], 10.0f, 80.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::SliderFloat("50米内 圈大小 建180", &imguiswitch_information.floatswitch[32], 10.0f, 250.f, "%.2f"))
									{
										SaveFile(".HP_SIVE");
									}
								}
								Custom::end_child();
							}
							break;
							case 3:
							{
								Custom::begin_child("压枪设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									static int selectedOption = -1;
									const char *options[] = {
										"站着", "蹲着", "趴着", "车上", "腰射", "机瞄",
										"红点", "二倍", "三倍", "四倍", "六倍", "八倍",
										"双跑", "马儿", "蹦蹦", "轿车", "吉普", "双人", "大船", "跑车", "风筝", "拉力", "电车"};
									float *values[] = {
										&imguiswitch_information.floatswitch[53], &imguiswitch_information.floatswitch[54],
										&imguiswitch_information.floatswitch[55], &imguiswitch_information.floatswitch[58],
										&imguiswitch_information.floatswitch[1], &imguiswitch_information.floatswitch[2],
										&imguiswitch_information.floatswitch[3], &imguiswitch_information.floatswitch[4],
										&imguiswitch_information.floatswitch[5], &imguiswitch_information.floatswitch[6],
										&imguiswitch_information.floatswitch[7], &imguiswitch_information.floatswitch[8],
										&imguiswitch_information.floatswitch[9], &imguiswitch_information.floatswitch[10],
										&imguiswitch_information.floatswitch[11], &imguiswitch_information.floatswitch[12],
										&imguiswitch_information.floatswitch[13], &imguiswitch_information.floatswitch[14],
										&imguiswitch_information.floatswitch[15], &imguiswitch_information.floatswitch[16],
										&imguiswitch_information.floatswitch[17], &imguiswitch_information.floatswitch[18],
										&imguiswitch_information.floatswitch[19]};
									for (int i = 0; i < IM_ARRAYSIZE(options); i++)
									{
										ImGui::PushID(i);
										bool isSelected = (selectedOption == i);
										if (ImGui::RadioButton(options[i], isSelected))
										{
											selectedOption = i;
										}
										ImGui::SameLine();
										ImGui::Text("(%.1f)", *values[i]);
										ImGui::SameLine(200);
										if ((i + 1) % 2 == 0 || i == IM_ARRAYSIZE(options) - 1)
										{
											ImGui::NewLine();
										}
										ImGui::PopID();
									}
									if (selectedOption != -1)
									{
										float *currentValue = values[selectedOption];
										ImGui::Text("正在修改: %s (当前值: %.1f)", options[selectedOption], *currentValue);
										if (ImGui::Button("-"))
										{
											*currentValue = ImClamp(*currentValue - 0.1f, -1.0f, 4.0f);
											SaveFile(".HP_SIVE");
										}
										if (ImGui::Button("+"))
										{
											*currentValue = ImClamp(*currentValue + 0.1f, -1.0f, 4.0f);
											SaveFile(".HP_SIVE");
										}
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("枪械力度", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									static int selectedWeapon = -1;
									const char *weapons[] = {
										"AKM", "M16A4", "SCAR-L", "M416", "Groza",
										"AUG", "QBZ", "M762", "Mk47", "G36C",
										"AC-VAL", "蜜獾", "法玛斯", "UZI", "UMP 5",
										"Vector", "汤姆逊", "野牛", "MP5K", "P90",
										"M249", "DP-28", "MG3", "PKM", "MG-36", "74u",
										"妹控", "ace", "js9", "mini4", "slr", "sks",
										"mk12", "m417", "mk20", "QBU", "ARX"};
									float *values[] = {
										&imguiswitch_information.floatswitch[60], &imguiswitch_information.floatswitch[61],
										&imguiswitch_information.floatswitch[62], &imguiswitch_information.floatswitch[63],
										&imguiswitch_information.floatswitch[64], &imguiswitch_information.floatswitch[65],
										&imguiswitch_information.floatswitch[66], &imguiswitch_information.floatswitch[67],
										&imguiswitch_information.floatswitch[68], &imguiswitch_information.floatswitch[69],
										&imguiswitch_information.floatswitch[70], &imguiswitch_information.floatswitch[71],
										&imguiswitch_information.floatswitch[72], &imguiswitch_information.floatswitch[73],
										&imguiswitch_information.floatswitch[74], &imguiswitch_information.floatswitch[75],
										&imguiswitch_information.floatswitch[76], &imguiswitch_information.floatswitch[77],
										&imguiswitch_information.floatswitch[78], &imguiswitch_information.floatswitch[79],
										&imguiswitch_information.floatswitch[80], &imguiswitch_information.floatswitch[81],
										&imguiswitch_information.floatswitch[82], &imguiswitch_information.floatswitch[83],
										&imguiswitch_information.floatswitch[84], &imguiswitch_information.floatswitch[85],
										&imguiswitch_information.floatswitch[86], &imguiswitch_information.floatswitch[87],
										&imguiswitch_information.floatswitch[88], &imguiswitch_information.floatswitch[89],
										&imguiswitch_information.floatswitch[90], &imguiswitch_information.floatswitch[91],
										&imguiswitch_information.floatswitch[92], &imguiswitch_information.floatswitch[93],
										&imguiswitch_information.floatswitch[94], &imguiswitch_information.floatswitch[95], 
										&imguiswitch_information.floatswitch[96]};
									for (int i = 0; i < IM_ARRAYSIZE(weapons); i++)
									{
										ImGui::PushID(i);
										bool isSelected = (selectedWeapon == i);
										if (ImGui::RadioButton(weapons[i], isSelected))
										{
											selectedWeapon = i;
										}
										ImGui::SameLine(200);
										if ((i + 1) % 2 == 0 || i == IM_ARRAYSIZE(weapons) - 1)
										{
											ImGui::NewLine();
										}
										ImGui::PopID();
									}
									if (selectedWeapon != -1)
									{
										float *currentValue = values[selectedWeapon];

										ImGui::Text("正在修改: %s (当前值: %.1f)", weapons[selectedWeapon], *currentValue);
										if (ImGui::Button("-"))
										{
											*currentValue = ImClamp(*currentValue - 0.1f, -1.0f, 5.0f);
											SaveFile(".HP_SIVE");
										}
										if (ImGui::Button("+"))
										{
											*currentValue = ImClamp(*currentValue + 0.1f, -1.0f, 5.0f);
											SaveFile(".HP_SIVE");
										}
									}
								}
								Custom::end_child();
							}
							break;
							case 4:
							{
								Custom::begin_child("优先设置", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
									if (ImGui::Combo("自瞄部位优先", &imguiswitch_information.intswitch[51], "瞄准头部\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Combo("自瞄倒地优先", &imguiswitch_information.intswitch[52], "倒地瞄准\0倒地不瞄\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Combo("自瞄选人优先", &imguiswitch_information.intswitch[53], "准心最近\0距离最近\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Combo("自瞄人机调整", &imguiswitch_information.intswitch[54], "瞄准人机\0不瞄人机\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Combo("自瞄范围调整", &imguiswitch_information.intswitch[55], "固定范围\0动态范围\0"))
									{
										SaveFile(".HP_SIVE");
									}
									if (ImGui::Combo("##触摸模式", &touch_information.touchLockMode, "软锁模式\0硬锁模式\0"))
									{
										SaveFile(".HP_SIVE");
										touch_information.TouchOrientationControl = true;
									}
								}
								Custom::end_child();
								ImGui::SameLine();
								Custom::begin_child("暂未开发", {((ImGui::GetWindowWidth() - 45.f) / 1.f - ImGui::GetStyle().ItemSpacing.x / 1.f) / 2.f, 0.f});
								{
								}
								Custom::end_child();
							}
							break;
							}
						}
						ImGui::EndGroup();
					}
					ImGui::EndChild();
				}
				break;
				}
			}
			ImGui::EndChild();
			ImGui::EndGroup();
			ImGui::PopItemFlag();
			ImGui::SetCursorPos({0, 0});
			if (ImGui::BeginChild("Navbar", {115.f + 180.f * Navbar_Width, ImGui::GetCurrentWindow()->Size.y}, false, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoScrollbar))
			{
				ImGui::GetWindowDrawList()->AddRectFilled({WindowPos.x, WindowPos.y}, {WindowPos.x + ImGui::GetWindowSize().x, WindowPos.y + ImGui::GetWindowSize().y}, ImGui::GetColorU32(ImGuiCol_ChildBg), 25.f, ImDrawFlags_RoundCornersLeft);
				ImGui::SetCursorPosY(110.f);
				ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, {0.f, 50.f});

				if (ImGui::Selectable("QC6.24秦川", Tab == 1))
					Tab = 1;
				ImGui::Spacing();
				if (ImGui::Selectable("QC秦川开关", Tab == 2))
					Tab = 2;
				ImGui::Spacing();
				if (ImGui::Selectable("梓喵设置", Tab == 3))
					Tab = 3;

				ImGui::PopStyleVar();
			}
			ImGui::EndChild();
		}
		ImGui::End();
	}
}
