#pragma once
#include "imgui.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "imgui_internal.h"
#include "imgui_tricks.h"

inline int Tab = 1;
inline int SubTab1 = 1;
inline int SubTab2 = 1;
inline int SubTab3 = 1;
inline int SubTab4 = 1;
inline float Content_Anim = 0;
namespace Custom {
    bool tab(const char* icon, const char* label, bool selected);
    bool subtab(const char* label, bool selected);
    void begin_child(const char* name, ImVec2 size);
    void end_child();
    bool collapse_button(bool collapsed);
    void ImRotateStart();
    ImVec2 ImRotationCenter();
    void ImRotateEnd(float rad, ImVec2 center = ImRotationCenter());
}
