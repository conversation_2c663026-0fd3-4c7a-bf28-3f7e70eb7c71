#include "Custom.h"

#include <string>
using namespace ImGui;
int rotation_start_index;
void Custom::ImRotateStart() {
    rotation_start_index = ImGui::GetWindowDrawList()->VtxBuffer.Size;
}
 
ImVec2 Custom::ImRotationCenter() {
    ImVec2 l(FLT_MAX, FLT_MAX), u(-FLT_MAX, -FLT_MAX);
    const auto& buf = ImGui::GetWindowDrawList()->VtxBuffer;
    for (int i = rotation_start_index; i < buf.Size; i++)
        l = ImMin(l, buf[i].pos), u = ImMax(u, buf[i].pos);
    return ImVec2((l.x + u.x) / 2, (l.y + u.y) / 2);
}

void Custom::ImRotateEnd(float rad, ImVec2 center) {
    float s = sin(rad), c = cos(rad);
    center = ImRotate(center, s, c) - center;
    auto& buf = ImGui::GetWindowDrawList()->VtxBuffer;
    for (int i = rotation_start_index; i < buf.Size; i++)
    	buf[i].pos = ImRotate(buf[i].pos, s, c) - center;
}

bool Custom::tab(const char* icon, const char* label, bool selected) {
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID(label);
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size({ window->Size.x, 85});
    ImRect bb(p, p + size);
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp(std::string(label).append( "tab.anim"), selected, 0, 1, 0.04);
    auto col = ImTricks::Animations::FastColorLerp(GetColorU32(ImGuiCol_TextDisabled), GetColorU32(ImGuiCol_Scheme), anim);
    if (pressed)
        Content_Anim = 0;
    static float line_pos = 0;
    line_pos = ImLerp(line_pos, selected ? bb.Min.y - window->Pos.y : line_pos, 0.04);
    window->DrawList->AddRectFilled({bb.Max.x - 3, window->Pos.y + line_pos}, {bb.Max.x, window->Pos.y + line_pos + size.y}, GetColorU32(ImGuiCol_Scheme, anim), 2, ImDrawFlags_RoundCornersLeft);
    PushStyleColor(ImGuiCol_Text, col.Value);
    PushFont(GetIO().Fonts->Fonts[1]);
    RenderText({bb.Min.x + 32, bb.GetCenter().y - CalcTextSize(icon).y / 2}, icon);
    PopFont();
    RenderText({bb.Min.x + 133, bb.GetCenter().y - CalcTextSize(label).y / 2}, label);
    PopStyleColor();
    return pressed;
}

bool Custom::subtab(const char* label, bool selected) {
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID(label);
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size({CalcTextSize(label, 0, true).x + 10, window->Size.y});
    ImRect bb(p, {p.x + size.x, p.y + size.y});
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp(std::string(label).append("subtab.anim"), selected, 0, 1, 0.04);
    auto col = ImTricks::Animations::FastColorLerp(GetColorU32(ImGuiCol_TextDisabled), GetColorU32(ImGuiCol_Scheme), anim);
    window->DrawList->AddRectFilled({bb.Min.x, bb.Max.y - 3}, bb.Max, GetColorU32(ImGuiCol_Scheme, anim), 2, ImDrawFlags_RoundCornersTop);
    PushStyleColor(ImGuiCol_Text, col.Value);
    RenderText(bb.GetCenter() - CalcTextSize(label, 0, true) / 2, label);
    PopStyleColor();
    return pressed;
}

void Custom::begin_child(const char* name, ImVec2 size) {
    ImGuiWindow* window = GetCurrentWindow();
    ImVec2 Pos = window->DC.CursorPos;
    BeginChild(std::string(name).append("mainnoe").c_str(), size, false, ImGuiWindowFlags_NoScrollbar);
    GetWindowDrawList()->AddText(NULL, 40, {Pos.x + 15, Pos.y + 15}, GetColorU32(ImGuiCol_Text), name);
    SetCursorPosY(70);
    BeginChild(std::string(name).append("maintwo").c_str(), {size.x, size.y == 0 ? size.y : size.y - 90}, false, ImGuiWindowFlags_NoBackground | ImGuiWindowFlags_NoScrollbar);
    SetCursorPosX(15);
    BeginGroup();
    PushStyleVar(ImGuiStyleVar_ItemSpacing, {10, 15});
}

void Custom::end_child() {
    PopStyleVar();
    EndGroup();
    EndChild();
    EndChild();
}

bool Custom::collapse_button(bool collapsed) {
    BeginChild("##123", {82, 82}, false, ImGuiWindowFlags_NoBackground);
    ImGuiWindow* window = GetCurrentWindow();
    ImGuiID id = window->GetID("collapse_button");
    ImVec2 p = window->DC.CursorPos;
    ImVec2 size(GetWindowSize());
    ImRect bb(p, p + size);
    ItemSize(bb);
    ItemAdd(bb, id);
    bool hovered, held;
    bool pressed = ButtonBehavior(bb, id, &hovered, &held);
    float anim = ImTricks::Animations::FastFloatLerp("collapse_button.anim", collapsed, 0, 1, 0.04);
    ImRotateStart();
    RenderArrow(window->DrawList, bb.GetCenter() - ImVec2(2 + 2 * !anim, 6), GetColorU32(ImGuiCol_Text), ImGuiDir_Down, 35);
    ImRotateEnd(3.14 * anim);
    EndChild();
    return pressed;
}
