#ifndef VECTOOL_H
#define VECTOOL_H
#include "imgui.h"

struct VecTor2 {
    float x;
    float y;
    VecTor2() {
        this->x = 0;
        this->y = 0;
    }
    VecTor2(float x, float y) {
        this->x = x;
        this->y = y;
    }
	bool operator!=(const VecTor2 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y){
			return true;
		}
		return false;
	}
    VecTor2 operator+(float v) const {
        return VecTor2(x + v, y + v);
    }
    VecTor2 operator-(float v) const {
        return VecTor2(x - v, y - v);
    }
    VecTor2 operator*(float v) const {
        return VecTor2(x * v, y * v);
    }
    VecTor2 operator/(float v) const {
        return VecTor2(x / v, y / v);
    }
    VecTor2& operator+=(float v) {
        x += v; y += v; return *this;
    }
    VecTor2& operator-=(float v) {
        x -= v; y -= v; return *this;
    }
    VecTor2& operator*=(float v) {
        x *= v; y *= v; return *this;
    }
    VecTor2& operator/=(float v) {
        x /= v; y /= v; return *this;
    }
    VecTor2 operator+(const VecTor2& v) const {
        return VecTor2(x + v.x, y + v.y);
    }
    VecTor2 operator-(const VecTor2& v) const {
        return VecTor2(x - v.x, y - v.y);
    }
    VecTor2 operator*(const VecTor2& v) const {
        return VecTor2(x * v.x, y * v.y);
    }
    VecTor2 operator/(const VecTor2& v) const {
        return VecTor2(x / v.x, y / v.y);
    }
    VecTor2& operator+=(const VecTor2& v) {
        x += v.x; y += v.y; return *this;
    }
    VecTor2& operator-=(const VecTor2& v) {
        x -= v.x; y -= v.y; return *this;
    }
    VecTor2& operator*=(const VecTor2& v) {
        x *= v.x; y *= v.y; return *this;
    }
    VecTor2& operator/=(const VecTor2& v) {
        x /= v.x; y /= v.y; return *this;
    }
};

struct VecTor3 {
    float x;
    float y;
    float z;
    VecTor3() {
        this->x = 0;
        this->y = 0;
        this->z = 0;
    }
    VecTor3(float x, float y, float z) {
        this->x = x;
        this->y = y;
        this->z = z;
    }
	bool operator!=(const VecTor3 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z){
			return true;
		}
		return false;
	}
    VecTor3 operator+(float v) const {
        return VecTor3(x + v, y + v, z + v);
    }
    VecTor3 operator-(float v) const {
        return VecTor3(x - v, y - v, z - v);
    }
    VecTor3 operator*(float v) const {
        return VecTor3(x * v, y * v, z * v);
    }
    VecTor3 operator/(float v) const {
        return VecTor3(x / v, y / v, z / v);
    }
    VecTor3& operator+=(float v) {
        x += v; y += v; z += v; return *this;
    }
    VecTor3& operator-=(float v) {
        x -= v; y -= v; z -= v; return *this;
    }
    VecTor3& operator*=(float v) {
        x *= v; y *= v; z *= v; return *this;
    }
    VecTor3& operator/=(float v) {
        x /= v; y /= v; z /= v; return *this;
    }
    VecTor3 operator+(const VecTor3& v) const {
        return VecTor3(x + v.x, y + v.y, z + v.z);
    }
    VecTor3 operator-(const VecTor3& v) const {
        return VecTor3(x - v.x, y - v.y, z - v.z);
    }
    VecTor3 operator*(const VecTor3& v) const {
        return VecTor3(x * v.x, y * v.y, z * v.z);
    }
    VecTor3 operator/(const VecTor3& v) const {
        return VecTor3(x / v.x, y / v.y, z / v.z);
    }
    VecTor3& operator+=(const VecTor3& v) {
        x += v.x; y += v.y; z += v.z; return *this;
    }
    VecTor3& operator-=(const VecTor3& v) {
        x -= v.x; y -= v.y; z -= v.z; return *this;
    }
    VecTor3& operator*=(const VecTor3& v) {
        x *= v.x; y *= v.y; z *= v.z; return *this;
    }
    VecTor3& operator/=(const VecTor3& v) {
        x /= v.x; y /= v.y; z /= v.z; return *this;
    }
};

struct VecTor4 {
    float x;
    float y;
    float z;
    float w;
    VecTor4() {
        this->x = 0;
        this->y = 0;
        this->z = 0;
        this->w = 0;
    }
    VecTor4(float x, float y, float z, float w) {
        this->x = x;
        this->y = y;
        this->z = z;
        this->w = w;
    }
	bool operator!=(const VecTor4 &Pos) {
		if (this->x != Pos.x || this->y != Pos.y || this->z != Pos.z || this->w != Pos.w){
			return true;
		}
		return false;
	}
	VecTor4 operator+(float v) const {
        return VecTor4(x + v, y + v, z + v, w + v);
    }
    VecTor4 operator-(float v) const {
        return VecTor4(x - v, y - v, z - v, w - v);
    }
    VecTor4 operator*(float v) const {
        return VecTor4(x * v, y * v, z * v, w * v);
    }
    VecTor4 operator/(float v) const {
        return VecTor4(x / v, y / v, z / v, w / v);
    }
    VecTor4& operator+=(float v) {
        x += v; y += v; z += v; w += v; return *this;
    }
    VecTor4& operator-=(float v) {
        x -= v; y -= v; z -= v; w -= v; return *this;
    }
    VecTor4& operator*=(float v) {
        x *= v; y *= v; z *= v; w *= v; return *this;
    }
    VecTor4& operator/=(float v) {
        x /= v; y /= v; z /= v; w /= v; return *this;
    }
    VecTor4 operator+(const VecTor4& v) const {
        return VecTor4(x + v.x, y + v.y, z + v.z, w + v.w);
    }
    VecTor4 operator-(const VecTor4& v) const {
        return VecTor4(x - v.x, y - v.y, z - v.z, w - v.w);
    }
    VecTor4 operator*(const VecTor4& v) const {
        return VecTor4(x * v.x, y * v.y, z * v.z, w * v.w);
    }
    VecTor4 operator/(const VecTor4& v) const {
        return VecTor4(x / v.x, y / v.y, z / v.z, w / v.w);
    }
    VecTor4& operator+=(const VecTor4& v) {
        x += v.x; y += v.y; z += v.z; w += v.w; return *this;
    }
    VecTor4& operator-=(const VecTor4& v) {
        x -= v.x; y -= v.y; z -= v.z; w -= v.w; return *this;
    }
    VecTor4& operator*=(const VecTor4& v) {
        x *= v.x; y *= v.y; z *= v.z; w *= v.w; return *this;
    }
    VecTor4& operator/=(const VecTor4& v) {
        x /= v.x; y /= v.y; z /= v.z; w /= v.w; return *this;
    }
};

typedef struct _TOUCH_INFORMATION
{
    float Scal;
    float TouchRadius;
    int TouchDeviceFile;
    VecTor2 TouchPoints;
    float floatswitch[15];
    bool TouchAimAtControl;
    VecTor2 TouchScreenSize;
    VecTor2 MouseCoordinate;
    VecTor2 AimingCoordinates;
    bool TouchOrientationControl;
    int touchLockMode;  // 0=软锁模式, 1=硬锁模式
} TOUCH_INFORMATION, *PTOUCH_INFORMATION;

typedef struct _RESOLUTION_INFORMATION {
	int Width;
	int Heiht;
	int Orientation;
	int ScreenWidth;
	int ScreenHeiht;
	int FixedScreenWidth;
	int FixedScreenHeiht;
} RESOLUTION_INFORMATION, *PRESOLUTION_INFORMATION;

typedef struct _IMGUISWITCH_INFORMATION {
	int intswitch[100];
	bool boolswitch[100];
	float floatswitch[100];
	ImColor colorswitch[100];
} IMGUISWITCH_INFORMATION, *PIMGUISWITCH_INFORMATION;

#endif
