#include "ImGuiTOOL.h"


struct SHARED_INFORMATION {//人物信息类
	int Team;//队伍
	int Death;//是否死亡
	int IsBoot;//人机
	int Blindage[2];//护甲信息
	float Health;//血量
	float Distance;//距离
	float ScreenCamera;//玩家在屏幕上的相机坐标
	char PlayerName[128];//名字
	ImColor ColorChanGeable;//颜色
	char HandheldWeaponName[128];//手持名字
	float HeadSkeletonCoordinates;//头部坐标
	struct VecTor2 RadarCoordinates;//雷达坐标
	struct VecTor4 ScreenCoordinates;//屏幕坐标
	struct VecTor2 ScreenCoordinateshou;//屏幕坐标0.3
	struct VecTor2 ScreenSkeletonSta[14];//
	struct VecTor2 ScreenSkeletonEnd[14];
	struct VecTor2 BottomLeftFootSkeleton;
    struct VecTor2 BottomRihtFootSkeleton;
	struct VecTor3 WorldSkeletonCoordinates[4];
	struct VecTor2 ScreenSkeletonCoordinates[4];
	struct VecTor3 VelocitySafety;
};

struct VEHICLE_INFORMATION {//车
	float Fuel;//油
	float Health;//血量
	float Distance;//距离
	float ScreenCamera;//屏幕上相机坐标
	struct VecTor4 ScreenCoordinates;//坐标
};

struct THROW_INFORMATION {
	float Distance;//距离
	float ScreenCamera;//屏幕相机
	struct VecTor4 ScreenCoordinates;//屏幕坐标类
};

struct SUNDRIES_INFORMATION {
	float Distance;
	float ScreenCamera;
	struct VecTor4 ScreenCoordinates;
};

struct RESOURCE_INFORMATION {
	float Distance;
	float ScreenCamera;
	struct VecTor4 ScreenCoordinates;
};

