#include <vector>
#include <iomanip> //cout输出16进制hex
#include "ImGuiELGS.h"
#include "touch.h"
#include "ImGuidraft.h"
#include <unordered_set>
#include <unordered_map>
int (*skeletonList)[2] = nullptr;
string ItemName;
touch touchdriven;
const int max_obj_number = 500;
int scid;
ImTextureID 图片;
ImVec4 BoneColor;
ImVec4 BoxColor;
ImVec4 LineColor;
struct SHARED_INFORMATION Shared_Information[max_obj_number];
struct VEHICLE_INFORMATION Vehicle_Information[max_obj_number];
struct THROW_INFORMATION Throw_Information[max_obj_number];
struct SUNDRIES_INFORMATION Sundries_Information[max_obj_number];
struct RESOURCE_INFORMATION Resource_Information[max_obj_number];
bool IsGameStart;
int aimitar = -1;
int aiminur = -1;
float aimi_abs = 0;
float aimi_x_abs = 0;
float aimi_y_abs = 0;
bool aimibot = false;
bool aimifal = false;
bool aimidage = false;
string aiminame = "";
int dz;
struct Timer aimitiem;
float aimidistance = 0;
bool aimiswitch = false;
unsigned long looptime = 0;
struct VecTor3 aimista = {0, 0, 0};
struct VecTor3 aimiend = {0, 0, 0};
struct VecTor3 aimiant = {0, 0, 0};
int OBJECTCOUNT[2] = {0, 0};
float LeftFoot_Skeleton = 0;
float RihtFoot_Skeleton = 0;
struct VecTor3 HeadSkeleton = {0, 0, 0};
struct VecTor3 SelfCoordinate = {0, 0, 0};
struct VecTor3 LeftFootSkeleton = {0, 0, 0};
struct VecTor3 RihtFootSkeleton = {0, 0, 0};
struct VecTor3 ObjectCoordinate = {0, 0, 0};
struct VecTor3 SelfViewCoordinate = {0, 0, 0};
static float initialThrowTime = 0.0f; // 记录掐雷开始的时间
static bool isHoldingGrenade = false; // 是否正在掐雷
VecTor3 VelocitySafety;
float bulletSpeed;
float bulletFlyTime;
float offsetRangeX = 20.0f;
float offsetRangeY = 20.0f;
float hz;
static int LastOwnTeam = -1;
uintptr_t YuCurrentVehicle;
float CurrentVehiclePressure = 0.6f;
extern int mode;
float RandomOffset(float range)
{
	return (rand() / (float)RAND_MAX) * range * 2 - range; // 生成 [-range, range] 的随机值
}
void ImGuiELGS::ImGuiWindowDraw()
{

	if (initializetouchorread)
	{
		InitializeColor();
		if (mode == 1)
		{
			thread *touchinformationthread = new thread([this]
														{ touchdriven.GetTouch(&touch_information, &resolution_information, AIMIO); });
			touchinformationthread->detach();
		}
		else if (mode == 2)
		{
			initializedraw = InitializeDrawing();
		}
		initializetouchorread = false;
	}
	ImDrawList *drawlist = ImGui::GetForegroundDrawList();
	if (initializedraw && Pid > 0)
	{
		IsGameStart = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0);
		if (IsGameStart)
		{
			if (imguiswitch_information.boolswitch[3])
			{
				drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43], ImColor(10, 10, 10, 80));
				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43], ImColor(255, 255, 255, 255), 0, 2);
				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43] / 2, ImColor(255, 255, 255, 255), 0, 2);
			}
			uintptr_t Controller = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0x88, 0x30, 0);
			uintptr_t CameraAddress = GetPointer(Controller, 0x608, 0);
			uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
			// uintptr_t Matrix2 = GetPointer(ModulesBase[0], 0x112313B0, 0x98, 0x740);
			uintptr_t SelfAddress = read<uintptr_t>(Controller + 0x32a8);
			WorldAddress = GetPointer(ModulesBase[0], 0x12161358, 0x90, 0);
			ArrayAddress = GetPointer(WorldAddress, 0xa0, 0);
			ArraysCount = read<int>(WorldAddress + 0xa8);
			if (WorldAddress != 0 || Controller != 0 || ArrayAddress != 0)
			{
				IsFire = read<int>(SelfAddress + 0x23e0);
				OwnTeam = read<int>(Controller + 0xb18);
				//	read(Matrix2, &SelfCoordinate, sizeof(SelfCoordinate));
				uintptr_t RootPoint = read<uintptr_t>(SelfAddress + 0x278);
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &SelfCoordinate, sizeof(SelfCoordinate));
				}
				read(CameraAddress + 0x600, &SelfViewCoordinate, sizeof(SelfViewCoordinate));							 // 向左
				read(Controller + 0x5a8, &touch_information.MouseCoordinate, sizeof(touch_information.MouseCoordinate)); // Rotator ControlRotation  向右
				自身武器 = read<int>(read<uintptr_t>(SelfAddress + 0xfe8) + 0xbd0);
				自身动作 = read<int>(read<uintptr_t>(SelfAddress + 0x1538));
				Fov = read<float>(read<uintptr_t>(read<uintptr_t>(SelfAddress + 0x5588) + 0x608) + 0x630);
				bulletSpeed = read<float>(read<uintptr_t>(read<uintptr_t>(SelfAddress + 0x33f0 + 0x20) + 0x19d0) + 0x13f4);
			}
			OBJECTCOUNT[0] = 0;
			OBJECTCOUNT[1] = 0;
			int 人机数量 = 0;
			int 真人数量 = 0;
			for (int count = 0; count < ArraysCount; count++)
			{
				uintptr_t ObjectAddress = read<uintptr_t>(ArrayAddress + 0x8 * count);
				read(ObjectAddress + 0xfdc, &Shared_Information[OBJECTCOUNT[0]].VelocitySafety, 12);
				if (ObjectAddress == 0 || ArrayAddress == ObjectAddress)
				{
					continue;
				}
				ItemName = GetClassName(ObjectAddress, 0x18); // 读取类名
				if (strlen(ItemName.c_str()) < 6 || strlen(ItemName.c_str()) >= 45)
				{
					continue;
				}
				YuCurrentVehicle = (read<uintptr_t>(SelfAddress + 0x1160));
				// 判断是当前乘坐的载具
				if (ObjectAddress == YuCurrentVehicle)
				{
					GetVehiclePressure(ItemName, CurrentVehiclePressure);
					// std::cout << "当前载具: " << ItemName << " 压力值: " << CurrentVehiclePressure << std::endl;
				}
				uintptr_t RootPoint = read<uintptr_t>(ObjectAddress + 0x278); // 对方坐标
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &ObjectCoordinate, sizeof(ObjectCoordinate));
				}
				if (ObjectCoordinate.x == 0 || ObjectCoordinate.y == 0 || ObjectCoordinate.z == 0)
				{
					continue;
				}
				if (read(MatrixAddress, &Matrix[0][0], 64))
				{
					touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
				}
				if (read<float>(ObjectAddress + 0x3518) == 479.5)
				{
					if (read<int>(ObjectAddress + 0xf58) & 1) // 死亡判断
					{
						continue;
					}
					GetDistance(ObjectCoordinate, SelfCoordinate, Shared_Information[OBJECTCOUNT[0]].Distance);
					Shared_Information[OBJECTCOUNT[0]].Team = read<int>(ObjectAddress + 0xac0); // 对象队伍
					if (Shared_Information[OBJECTCOUNT[0]].Team == OwnTeam)
					{
						continue;
					} // 队伍等于自己队伍或队伍不绘制
					Shared_Information[OBJECTCOUNT[0]].Health = read<float>(ObjectAddress + 0xed8) * 100 / read<float>(ObjectAddress + 0xee0); // 当前血量
					if (Shared_Information[OBJECTCOUNT[0]].Health > 100)
					{
						continue;
					} // 血量大于100不绘制
					Shared_Information[OBJECTCOUNT[0]].IsBoot = read<int>(ObjectAddress + 0xadc); // 人机判断
					if (imguiswitch_information.boolswitch[10] && Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						continue;
					}
					if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						人机数量++;
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[6]; // 人机颜色
						BoneColor = imguiswitch_information.colorswitch[5];											 // 人机颜色
						BoxColor = imguiswitch_information.colorswitch[5];											 // 人机颜色
						LineColor = imguiswitch_information.colorswitch[5];
						sprintf(Shared_Information[OBJECTCOUNT[0]].PlayerName, "人机%d", OBJECTCOUNT[0]);
					}
					else if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 0)
					{
						真人数量++;
						BoneColor = imguiswitch_information.colorswitch[8];
						BoxColor = imguiswitch_information.colorswitch[7];
						LineColor = imguiswitch_information.colorswitch[9];
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[4];	// 真人颜色
						GetUTF8(Shared_Information[OBJECTCOUNT[0]].PlayerName, read<uintptr_t>(ObjectAddress + 0xa40)); // 获取真人名字
					}
					else
					{
						人机数量++;
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[6]; // 高级人机颜色
						BoneColor = imguiswitch_information.colorswitch[5];											 // 高级人机颜色
						BoxColor = imguiswitch_information.colorswitch[5];											 // 高级人机颜色
						LineColor = imguiswitch_information.colorswitch[5];
						sprintf(Shared_Information[OBJECTCOUNT[0]].PlayerName, "高级人机%d", OBJECTCOUNT[0]);
					}
					// 骨骼一列的东西
					MeshAddress = read<uintptr_t>(ObjectAddress + 0x600);
					if (read<int>(MeshAddress) != 0)
					{
						ReadBone(MeshAddress + 0x1F0, MeshTrans);
						TransformTurnMatrix(XMatrix, MeshTrans);
					}
					BoneAddress = read<uintptr_t>(MeshAddress + 0x7F8) + 0x30;
					GetBoneTransform(5, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[0]); // 头部骨骼点
					GetBoneTransform(0, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[1]); // 胸部骨骼点

					int skeletonType = read<int>(MeshAddress + 0x7F8 + 0x8);
					switch (skeletonType)
					{
					case 68:
						skeletonList = SkeletonList_New;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(57, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(61, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					case 70:
						skeletonList = SkeletonList_S30;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(57, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(61, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					case 72:
						skeletonList = SkeletonList_Yan;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(59, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(63, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					default:
						skeletonList = SkeletonList_Old;
						if (skeletonList != nullptr)
						{
							GetBoneTransform(54, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
							GetBoneTransform(58, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
						}
						break;
					}

					LeftFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1];
					LeftFootSkeleton.z -= 20;
					WorldTurnScreen(LeftFoot_Skeleton, LeftFootSkeleton);

					RihtFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2];
					RihtFootSkeleton.z -= 20;
					WorldTurnScreen(RihtFoot_Skeleton, RihtFootSkeleton);

					HeadSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0];
					HeadSkeleton.z += 20;
					WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, HeadSkeleton);

					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.y = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y = max(LeftFoot_Skeleton, RihtFoot_Skeleton);
					if (WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCamera, ObjectCoordinate))
					{
						if (imguiswitch_information.boolswitch[0] && Shared_Information[OBJECTCOUNT[0]].Team != -1)
						{
							ImColor currentBoneColor = BoneColor;
							if (!aimidage && imguiswitch_information.boolswitch[21] && OBJECTCOUNT[0] == aimitar)
							{
								currentBoneColor = imguiswitch_information.colorswitch[6];
							}
							for (int i = 0; i < 14; i++)
							{
								GetBoneTransform(skeletonList[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
								GetBoneTransform(skeletonList[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);

								skeleton(drawlist,
										 {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].y},
										 {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].y},
										 currentBoneColor,
										 imguiswitch_information.floatswitch[57]);
							}
						}
						if (imguiswitch_information.boolswitch[8] && 自身武器 == 0) // 名字绘制
						{
							string Text = Shared_Information[OBJECTCOUNT[0]].PlayerName;
							ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 18);
							drawlist->AddText(NULL, 18, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 20 - (TextSize.y / 2)}, imguiswitch_information.colorswitch[1], Text.c_str());
						}
						if (imguiswitch_information.boolswitch[1])
						{ // 方框绘制
							DrawPlayerBox(ImGui::GetForegroundDrawList(), Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.y, BoxColor, imguiswitch_information.floatswitch[57]);
						}
						if (imguiswitch_information.boolswitch[2])
						{
							// 射线绘制
							ImVec2 start = {resolution_information.Width, 0};
							ImVec2 end = {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 60};
							float thickness = imguiswitch_information.floatswitch[57];
							ImU32 lineColor = ImGui::GetColorU32(LineColor);
							drawlist->AddLine(start, end, lineColor, thickness);
						}

						if (imguiswitch_information.boolswitch[6])
						{ // 血条绘制
							HealthBarType healthBarType;
							switch (imguiswitch_information.intswitch[1])
							{
							case 0:
								healthBarType = CircleArc;
								break;
							case 1:
								healthBarType = RectangleFilled;
								break;
							case 2:
								healthBarType = CustomRectangle;
							}
							DrawHealthBar(drawlist, Shared_Information, healthBarType, imguiswitch_information, OBJECTCOUNT[0]);
						}

						if (imguiswitch_information.boolswitch[7]) // 距离绘制
						{
							// 绘制队伍
							string TeamText = to_string((int)Shared_Information[OBJECTCOUNT[0]].Team);
							ImVec2 TeamTextSize = ImGui::CountTextSize(NULL, TeamText.c_str(), 22);
							drawlist->AddText(NULL, 22, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TeamTextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 70 - (TeamTextSize.y / 2)}, imguiswitch_information.colorswitch[2], TeamText.c_str());
							// 绘制距离
							string DistanceText = to_string((int)Shared_Information[OBJECTCOUNT[0]].Distance);
							ImVec2 DistanceTextSize = ImGui::CountTextSize(NULL, DistanceText.c_str(), 20);
							drawlist->AddText(NULL, 20, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (DistanceTextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 45 - (DistanceTextSize.y / 2)}, imguiswitch_information.colorswitch[0], DistanceText.c_str());
							// 检查动作是否在 恰雷动作范围内
							if ((自身武器 == 602004 || 自身武器 == 9825004) &&
								((自身动作 >= 65000 && 自身动作 < 66000) || 自身动作 == 69665 || 自身动作 == 69648 || 自身动作 == 69664 || 自身动作 == 69649))
							{
								if (Shared_Information[OBJECTCOUNT[0]].Distance < 130)
								{
									if (!isHoldingGrenade) // 第一次进入掐雷动作时记录时间
									{
										initialThrowTime = ImGui::GetTime(); // 记录掐雷的开始时间
										isHoldingGrenade = true;
									}
									float instantExplosionTime = 7.0f - (ImGui::GetTime() - initialThrowTime) - (Shared_Information[OBJECTCOUNT[0]].Distance / 18.0f);
									char buffer[16];
									snprintf(buffer, sizeof(buffer), "%.1f", instantExplosionTime);
									std::string TimeText = buffer;
									ImVec2 TimeTextSize = ImGui::CalcTextSize(TimeText.c_str());
									float centeredX = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TimeTextSize.x / 2);
									绘制加粗文本(
										50,
										centeredX,
										Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 150 - (TimeTextSize.y / 2),
										ImColor(255, 0, 0, 255),
										ImColor(ImVec4(0 / 255.f, 0 / 255.f, 0 / 255.f, 0.7f)),
										TimeText.c_str());
								}
							}
							else
							{
								if (isHoldingGrenade)
								{
									isHoldingGrenade = false;
									initialThrowTime = 0;
								}
							}
						}
						if (imguiswitch_information.boolswitch[9] &&
							(scid = read<int>(read<uintptr_t>(ObjectAddress + 0xfe8) + 0xbd0)) > 0 &&
							获取枪械信息(scid, &图片))
						{
							drawlist->AddImage(
								图片,
								ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - 55, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 125),
								ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + 59, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 90));
						}

						if (initializeaimi && imguiswitch_information.boolswitch[52] && aiminur == OBJECTCOUNT[0] && mode == 1)
						{
							drawlist->AddLine({resolution_information.Width, resolution_information.Heiht}, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y}, ImColor(255, 255, 255, 255), 2);
						}
					}

					if (imguiswitch_information.boolswitch[4] && Shared_Information[OBJECTCOUNT[0]].IsBoot == 0 && Shared_Information[OBJECTCOUNT[0]].Health > 0)
					{ // 敌背绘制
						WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou, ObjectCoordinate, false);
						ImColor arrowColor;
						if (Shared_Information[OBJECTCOUNT[0]].Distance <= 75.0f)
						{
							arrowColor = ImColor(255, 0, 0, 255); // 红色（75米内）
						}
						else if (Shared_Information[OBJECTCOUNT[0]].Distance <= 150.0f)
						{
							arrowColor = ImColor(255, 255, 0, 255); // 黄色（75-150米）
						}
						else
						{
							arrowColor = ImColor(0, 255, 0, 255); // 绿色（150米外）
						}

						OffScreen(
							VecTor2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.x,
									Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.y),
							Shared_Information[OBJECTCOUNT[0]].ScreenCamera,
							arrowColor,
							imguiswitch_information.floatswitch[50] + dx + Shared_Information[OBJECTCOUNT[0]].Distance * 0.3);
					}

					if (imguiswitch_information.boolswitch[3])
					{ // 雷达绘制
						GetRadarCoordinates(Shared_Information[OBJECTCOUNT[0]].RadarCoordinates, touch_information.MouseCoordinate.y, ObjectCoordinate, SelfCoordinate, imguiswitch_information.floatswitch[42]);

						if (!透明)
						{
							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, 5, ImColor(255, 255, 255, 255));
						}
						if (CapTivity(imguiswitch_information.floatswitch[43], Shared_Information[OBJECTCOUNT[0]].RadarCoordinates))
						{

							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.x, resolution_information.Heiht + imguiswitch_information.floatswitch[41] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.y}, 5, Shared_Information[OBJECTCOUNT[0]].ColorChanGeable);
						}
					}
					OBJECTCOUNT[0]++;
				}

				if (!ItemName.empty() && ItemName.find("PlayerPawn") == std::string::npos)
				{
					GetDistance(ObjectCoordinate, SelfCoordinate, Throw_Information[OBJECTCOUNT[1]].Distance);
					std::string matchedName = "";
					bool isOnScreen = WorldTurnScreen(Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates, Throw_Information[OBJECTCOUNT[1]].ScreenCamera, ObjectCoordinate);
					if (isOnScreen)
					{
						if (imguiswitch_information.boolswitch[61])
						{
							std::string debugText = ItemName + "_" + std::to_string(static_cast<int>(Throw_Information[OBJECTCOUNT[1]].Distance)) + "米";
							ImVec2 debugTextSize = ImGui::CountTextSize(NULL, debugText.c_str(), 12);
							绘制加粗文本(23, Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.x - (debugTextSize.x / 2),
										 Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.y,
										 ImColor(ImVec4(1.0f, 1.0f, 1.0f, 1.0f)), ImColor(ImVec4(0.0f, 0.0f, 0.0f, 0.7f)), debugText.c_str());

							if (Throw_Information[OBJECTCOUNT[1]].Distance <= 100)
							{
								std::ofstream outFile("Debug.txt", std::ios::app);
								if (outFile.is_open())
								{
									outFile << "类名: " << ItemName << ", 距离: " << Throw_Information[OBJECTCOUNT[1]].Distance << "m" << std::endl;
									outFile.close();
								}
								else
								{
									std::cerr << "Unable to open file for writing." << std::endl;
								}
							}
						}
						else
						{
							struct ItemConfig
							{
								std::string name;  // 物资名称
								ImColor color;	   // 物资颜色
								float minDistance; // 最小距离限制
								float maxDistance; // 最大距离限制
								bool valid;		   // 是否满足条件
							};
							// 配置物资类型及其属性
							std::vector<ItemConfig> itemConfigs = {
								{"投掷物", ImColor(ImVec4(1.0f, 0.0f, 0.0f, 0.95f)), 0, 300, imguiswitch_information.boolswitch[53] && isPartialMatchedType(1, ItemName, matchedName)},
								{"载具", ImColor(ImVec4(0.0f, 1.0f, 0.0f, 0.95f)), 10, 600, imguiswitch_information.boolswitch[54] && 自身武器 == 0 && isPartialMatchedType(2, ItemName, matchedName)},
								{"防具", ImColor(ImVec4(0.118f, 0.565f, 1.0f, 0.95f)), 0, 100, imguiswitch_information.boolswitch[55] && 自身武器 == 0 && isPartialMatchedType(3, ItemName, matchedName)},
								{"道具", ImColor(ImVec4(1.0f, 1.0f, 0.0f, 0.95f)), 0, 1700, imguiswitch_information.boolswitch[56] && 自身武器 == 0 && isPartialMatchedType(4, ItemName, matchedName)},
								{"盒子", ImColor(ImVec4(0.647f, 0.165f, 0.165f, 0.95f)), 0, 100, imguiswitch_information.boolswitch[57] && 自身武器 == 0 && isPartialMatchedType(5, ItemName, matchedName)},
								{"药", ImColor(ImVec4(0.5f, 0.0f, 0.5f, 0.95f)), 0, 100, imguiswitch_information.boolswitch[58] && 自身武器 == 0 && isPartialMatchedType(6, ItemName, matchedName)},
								{"子弹", ImColor(ImVec4(0.914f, 0.184f, 0.024f, 0.95f)), 0, 100, imguiswitch_information.boolswitch[59] && 自身武器 == 0 && isPartialMatchedType(7, ItemName, matchedName)}, // bShowAirLine
								{"未开箱", ImColor(ImVec4(1.0f, 0.0f, 0.0f, 0.95f)), 0, 600, imguiswitch_information.boolswitch[60] && 自身武器 == 0 && isPartialMatchedType(8, ItemName, matchedName) && read<int>(ObjectAddress + 0x270) == 0}};

							// 遍历所有物资类型并处理
							for (const auto &config : itemConfigs)
							{
								// 检查是否有效并在距离限制内
								if (config.valid && Throw_Information[OBJECTCOUNT[1]].Distance >= config.minDistance && Throw_Information[OBJECTCOUNT[1]].Distance <= config.maxDistance)
								{
									// 绘制匹配的物资信息
									std::string displayText = matchedName + "_" + std::to_string(static_cast<int>(Throw_Information[OBJECTCOUNT[1]].Distance)) + "米";
									ImVec2 textSize = ImGui::CountTextSize(NULL, displayText.c_str(), 12);
									绘制加粗文本(23, Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.x - (textSize.x / 2),
												 Throw_Information[OBJECTCOUNT[1]].ScreenCoordinates.y,
												 config.color, ImColor(ImVec4(0.0f, 0.0f, 0.0f, 0.7f)), displayText.c_str());
									OBJECTCOUNT[1]++;
									break; // 匹配到一个物资后立即退出循环
								}
							}
						}
					}
				}
			}
			if (imguiswitch_information.intswitch[59] && mode == 1)
			{
				bool isShotgun = false;
				bool isSniper = false;
				float weaponPressure = 0.6f;
				float submachinePrediction;
				WeaponPressureGunValue(自身武器, Fov, isShotgun, isSniper, weaponPressure, submachinePrediction);
				// std::cout << "Is Shotgun: " << (isShotgun ? "Yes" : "No") << std::endl;
				// std::cout << "Is Sniper: " << (isSniper ? "Yes" : "No") << std::endl;
				//  std::cout << "Weapon Pressure: " << weaponPressure << std::endl;
				// std::cout << "Fov: " << Fov << std::endl;
				if (imguiswitch_information.boolswitch[49] && isSniper || (!imguiswitch_information.boolswitch[48] && isShotgun))
				{
					aimiswitch = false;
					touchdriven.setAimIo(false);
					aimidage = false;
					memset(&aimiant, 0, sizeof(aimiant));
					aimitar = -1;
					aimidistance = 0;
					memset(&aimiend, 0, sizeof(aimiend));
					memset(&aimista, 0, sizeof(aimista));
				}
				else
				{
					if (isShotgun)
					{
						imguiswitch_information.intswitch[51] = 1;
					}
					else
					{
						imguiswitch_information.intswitch[51] = 0;
					}
					if (!aimiswitch)
					{
						aimi_abs = 0;
						aimitar = -1;
						for (int i = 0; i < OBJECTCOUNT[0]; i++)
						{
							aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[i].Health <= 0);
							aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[i].IsBoot == 1);
							aimi_x_abs = abs(resolution_information.Width - Shared_Information[i].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x);
							aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[i].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y);
							if (Shared_Information[i].ScreenCamera > 0.01 && (Shared_Information[i].Distance <= imguiswitch_information.floatswitch[51] || Shared_Information[i].Distance <= imguiswitch_information.floatswitch[56]) && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !aimidage && aimi_x_abs < imguiswitch_information.floatswitch[50] && aimi_y_abs < imguiswitch_information.floatswitch[50])
							{
								switch (imguiswitch_information.intswitch[53])
								{
								case 0:
								{
									if (aimi_abs == 0)
									{
										aimitar = i;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
									else
									{
										if (aimi_abs > aimi_x_abs + aimi_y_abs)
										{
											aimitar = i;
											aimi_abs = aimi_x_abs + aimi_y_abs;
										}
									}
								}
								break;
								case 1:
								{
									if (aimi_abs == 0)
									{
										aimitar = i;
										aimidistance = Shared_Information[i].Distance;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
									else
									{
										if (aimidistance > Shared_Information[i].Distance)
										{
											aimitar = i;
											aimidistance = Shared_Information[i].Distance;
											aimi_abs = aimi_x_abs + aimi_y_abs;
										}
									}
								}
								break;
								}
							}
						}
						if (aimitar == -1)
						{
							aimiswitch = false;
						}
						else if (aimitar > -1)
						{
							aimiswitch = true;
							aimiant = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
						}
						aiminur = aimitar;
					}
					aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[aimitar].Health <= 0);
					aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[aimitar].IsBoot == 1);
					aimi_x_abs = abs(resolution_information.Width - Shared_Information[aimitar].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x);
					aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[aimitar].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y);
					if (Shared_Information[aimitar].ScreenCamera > 0.01 &&
						((Fov == 80 || Fov == 90) ? Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[49] : Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51]) &&
						imguiswitch_information.intswitch[59] &&
						!aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch &&
						((isShotgun && Shared_Information[aimitar].Distance <= 20) ||
						 (IsFire == 1 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51])) &&
						aimi_x_abs < imguiswitch_information.floatswitch[50] &&
						aimi_y_abs < imguiswitch_information.floatswitch[50])
					{
						bulletFlyTime = (Shared_Information[aimitar].Distance / bulletSpeed) * imguiswitch_information.floatswitch[52] * 100.0f;
						;
						aimiant = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]] + Shared_Information[aimitar].VelocitySafety * bulletFlyTime;
						if ((IsFire == 1 || IsFire == 257) || isShotgun)
						{
							if (自身动作 == 272 || 自身动作 == 1296 || 自身动作 == 1297 || 自身动作 == 273 || 自身动作 == 4368 || 自身动作 == 4369 || 自身动作 == 5392 || 自身动作 == 5393 || 自身动作 == 400 || 自身动作 == 403) // 站着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[53] * weaponPressure;
								}
							}
							else if (自身动作 == 288 || 自身动作 == 289 || 自身动作 == 4384 || 自身动作 == 4385 || 自身动作 == 5408 || 自身动作 == 5409 || 自身动作 == 1312 || 自身动作 == 1313) // 蹲着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[54] * weaponPressure;
								}
							}
							else if (自身动作 == 320 || 自身动作 == 1344) // 趴着动作
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[55] * weaponPressure;
								}
							}
							else if (YuCurrentVehicle != 0) // 车上
							{
								if (Shared_Information[aimitar].Distance)
								{
									SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[58] * CurrentVehiclePressure;
								}
							}
							touchdriven.setFireIo(true);
						}
						else
						{
							touchdriven.setFireIo(false);
						}
						if (aimiant.x != 0 || aimiant.y != 0 || aimiant.z != 0)
						{
							touch_information.AimingCoordinates = FastAtan2(aimiant, SelfViewCoordinate);
						}
						aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[aimitar].Health <= 0);
						aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[aimitar].IsBoot == 1);

						if (imguiswitch_information.boolswitch[48] && isShotgun)
						{
							if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= 20 && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && isShotgun && Shared_Information[aimitar].IsBoot == 0 && Shared_Information[aimitar].Health > 0 && aimi_x_abs < imguiswitch_information.floatswitch[50] && aimi_y_abs < imguiswitch_information.floatswitch[50])
							{
								touchdriven.setAimIo(true);
							}
							else
							{
								aimiswitch = false;
								touchdriven.setAimIo(false);
								memset(&aimiant, 0, sizeof(aimiant));
							}
						}
						else
						{
							if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51] && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && (IsFire == 1) && (自身武器 == 103013 || 自身武器 == 103014 || 自身武器 == 104005 || 自身武器 == 9810043 || 自身武器 == 9810041 || Shared_Information[aimitar].Health <= imguiswitch_information.intswitch[30]))
							{
								touchdriven.setAimIo(true);
							}
							else
							{
								aimiswitch = false;
								touchdriven.setAimIo(false);
								memset(&aimiant, 0, sizeof(aimiant));
							}
						}
					}
					else
					{
						aimiswitch = false;
						touchdriven.setAimIo(false);
						memset(&aimiant, 0, sizeof(aimiant));
					}
				}
			}
			float CoordinatesW = resolution_information.ScreenWidth * touch_information.TouchRadius;
			float CoordinatesX = resolution_information.ScreenWidth * touch_information.TouchPoints.y + RandomOffset(offsetRangeX);
			float CoordinatesY = resolution_information.ScreenHeiht * (1 - touch_information.TouchPoints.x) + RandomOffset(offsetRangeY);
			if (initializeaimi && imguiswitch_information.boolswitch[50])
			{
				string Text = "";
				Text += "触摸控制区域";
				ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 30);
				drawlist->AddText(NULL, 30, {CoordinatesX - (TextSize.x / 2), CoordinatesY - (TextSize.y / 2)}, ImColor(255, 255, 255, 255), Text.c_str());
				drawlist->AddRectFilled({CoordinatesX - CoordinatesW, CoordinatesY - CoordinatesW}, {CoordinatesX + CoordinatesW, CoordinatesY + CoordinatesW}, ImColor(200, 0, 0, 100));
			}
			if (initializeaimi && imguiswitch_information.intswitch[59] && mode == 1)
			{
				if (imguiswitch_information.boolswitch[51])
				{
					float radius = 0.0f;
					if (imguiswitch_information.intswitch[55] == 0) // 固定范围
					{
						radius = imguiswitch_information.floatswitch[50];
					}
					else if (imguiswitch_information.intswitch[55] == 1) // 动态范围
					{
						float minRange = imguiswitch_information.floatswitch[31];
						float maxRange = imguiswitch_information.floatswitch[32];

						if (aimitar == -1) // 未锁定目标时只使用最大范围
						{
							radius = maxRange;
							imguiswitch_information.floatswitch[50] = maxRange; // 更新自瞄范围为最大值
						}
						else // 锁定目标时使用动态范围
						{
							float targetDistance = Shared_Information[aimitar].Distance;
							// 根据目标距离动态计算范围
							if (targetDistance <= 50.0f)
							{
								// 在50米内时，范围随距离动态变化
								radius = minRange + (maxRange - minRange) * (1.0f - targetDistance / 50.0f);
							}
							else
							{
								// 超过50米时使用最小范围
								radius = minRange;
							}
							imguiswitch_information.floatswitch[50] = radius; // 更新自瞄范围为动态计算的值
						}
					}
					// 绘制圆圈
					drawlist->AddCircle({resolution_information.Width, resolution_information.Heiht}, radius, ImColor(255, 255, 255, 255), 0, 2);
				}
			}
			if (imguiswitch_information.boolswitch[30])
			{
				std::string playerText = std::string("P: ") + (真人数量 < 10 ? "0" : "") + std::to_string(真人数量);
				std::string botText = std::string("A: ") + (人机数量 < 10 ? "0" : "") + std::to_string(人机数量);
				auto textSize = ImGui::GetFont()->CalcTextSizeA(60, FLT_MAX, -1, (playerText + "    " + botText).c_str(), NULL, NULL);
				float px = resolution_information.Width;
				float py = resolution_information.Heiht * 0.13f;
				ImVec2 textPos(px - textSize.x / 2, py - textSize.y / 2);
				auto playerTextSize = ImGui::GetFont()->CalcTextSizeA(60, FLT_MAX, -1, playerText.c_str(), NULL, NULL);
				ImVec2 playerTextPos(textPos.x, textPos.y);
				ImVec2 botTextPos(textPos.x + playerTextSize.x + 20.0f, textPos.y);
				ImU32 outlineColor1 = ImColor(0, 0, 0, 255);
				ImU32 playerColor = imguiswitch_information.colorswitch[8];
				ImU32 botColor = imguiswitch_information.colorswitch[5];
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(playerTextPos.x - 0.8f, playerTextPos.y - 0.8f), outlineColor1, playerText.c_str());
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(playerTextPos.x + 0.8f, playerTextPos.y + 0.8f), outlineColor1, playerText.c_str());
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, playerTextPos, playerColor, playerText.c_str());
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(botTextPos.x - 0.8f, botTextPos.y - 0.8f), outlineColor1, botText.c_str());
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, ImVec2(botTextPos.x + 0.8f, botTextPos.y + 0.8f), outlineColor1, botText.c_str());
				ImGui::GetBackgroundDrawList()->AddText(NULL, 60, botTextPos, botColor, botText.c_str());
			}
		}
		else if (!IsGameStart && imguiswitch_information.boolswitch[30])
		{
			string Text = "";
			Text += "QC 掉血 6.24秦川";
			ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 42);
			drawlist->AddText(NULL, 42, {resolution_information.Width - (TextSize.x / 2), 40 - (TextSize.y / 2)}, ImColor(255, 0, 0, 255), Text.c_str());
		}
	}
}
